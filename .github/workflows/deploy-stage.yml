name: Deploy to Stage

on:
  workflow_run:
    workflows:
      - Run Tests
    types:
      - completed
    branches:
      - main

concurrency:
  group: staging-environment
  cancel-in-progress: false

jobs:
  deploy:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies (root)
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Install dependencies (infrastructure)
        working-directory: ./infrastructure
        run: npm ci

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Setup Pulumi
        uses: pulumi/actions@v4

      - name: Deploy infrastructure to stage
        working-directory: ./infrastructure
        run: pulumi up --yes --non-interactive --skip-preview --stack stage
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
