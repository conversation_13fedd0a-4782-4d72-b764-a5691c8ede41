name: Run Tests

on:
  push:
    branches:
      - '**'

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm run test:ci

      - name: Upload test coverage
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-report
          path: coverage/
          retention-days: 5

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: coverage/
          retention-days: 5

      - name: Test Reporter
        uses: dorny/test-reporter@v2.1.0
        if: always()
        with:
          name: Vitest Results
          path: reports/junit.xml
          reporter: jest-junit
