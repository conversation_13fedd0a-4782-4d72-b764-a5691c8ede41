config:
  jfg-backend:logLevel: DEBUG
  jfg-backend:googleClientId: 895242317375-tb01eubvnh50vt1osn4enagk5j19e1q3.apps.googleusercontent.com
  jfg-backend:googleClientSecret:
    secure: AAABAGWMRXpw8nM4sC8xJLosmSN5nFaplndqJqfPrahirm0mBE3Yq20lTDnVSqPvvLjXszNuej34JKg2URk30ZL8Jg==
  jfg-backend:alarmEmail: <EMAIL>
  jfg-backend:databaseType: postgres
  jfg-backend:postgresUrl: db.firmrxnzhlkubwtpbsny.supabase.co
  jfg-backend:postgresUser: postgres
  jfg-backend:postgresPassword:
    secure: AAABAB4RGf0QZ51ZF7tacTLlR/crvLu299051iUHTdSH+o5+kVClF7F2BBDHLg==
  jfg-backend:mjmlAppId:
    secure: AAABAG8M1rWEQCZNY2ssoVVPZ9SJvlcQLp0lKhjfpbGoNP+Z85Kk+x5k8Fq2B9Hu8xs5uyLKILck54wWoB2UxCc/wZ0=
  jfg-backend:mjmlSecret:
    secure: AAABAG523xLLku7ptTIsmwlpxLaVIp/qsJQZO0FJypO2CbBk8csNfo2njvLr4s72lCAYZWYmvdXNIDnXqT+9O0vSAwI=
  jfg-backend:postgresPort: "6543"
  jfg-backend:notionApiKey:
    secure: AAABACvoyz1O7r9on8SMvTgBUesdfgLNqZ7MA5IEv+cnpIldS8scbLq9LIOHqnCucoFWLIpysX9KJiI+cCBjhl4F6WXTa1Cte+P5AlV0Ub9Izw==
  jfg-backend:notionDbId:
    secure: AAABAJyQRJDGqpXiN607sDZ+fk6JxTgL6wPyuJm01hfULLi4Nxml9CkPp8conYCiVHvcgsG20Hb7tnd4M9wVXg==
  aws:region: us-east-2
