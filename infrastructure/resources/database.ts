import { createDynamoDbTable } from '../dynamodb';

export function createDatabaseTables() {
  const fixtureDetailTable = createDynamoDbTable('fixtureDetailTable', {
    attributes: [
      { name: 'fixtureId', type: 'S' },
      { name: 'gameworldId', type: 'S' },
      { name: 'leagueId', type: 'S' },
    ],
    globalSecondaryIndexes: [
      {
        name: 'gameworldId_leagueId_index',
        hashKey: 'gameworldId',
        rangeKey: 'leagueId',
        projectionType: 'KEYS_ONLY',
      },
    ],
    hashKey: 'fixtureId',
    billingMode: 'PAY_PER_REQUEST',
  });

  const inboxTable = createDynamoDbTable('inboxTable', {
    attributes: [
      { name: 'gameworldId_teamId', type: 'S' },
      { name: 'id', type: 'S' },
    ],
    hashKey: 'gameworldId_teamId',
    rangeKey: 'id',
    billingMode: 'PAY_PER_REQUEST',
  });

  return {
    fixtureDetailTable,
    inboxTable,
  };
}
