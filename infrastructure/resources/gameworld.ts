import { Table } from '@pulumi/aws/dynamodb';
import { createLambdaErrorLogAlarm } from '../cloudwatch';
import { sqsBatchWindow, sqsBatchWindowMaximum, sqsBatchWindowMinimum } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createScheduledRule } from '../eventBridge';
import { createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface GameworldResourcesConfig {
  fixtureDetailTable: Table;
}

export function createGameworldResources({ fixtureDetailTable }: GameworldResourcesConfig) {
  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('gameworld-error');

  // Create unattached players queue for direct access
  const unattachedPlayersDLQ = createDLQ('gameworldUnattachedPlayersQueue');
  const unattachedPlayersQueue = createQueue(
    'gameworldUnattachedPlayersQueue',
    unattachedPlayersDLQ
  );

  const generatePlayersDLQ = createDLQ('generatePlayersQueue');
  const generatePlayersQueue = createQueue('generatePlayersQueue', generatePlayersDLQ, 3);

  // Create team queue for direct access
  const teamDLQ = createDLQ('gameworldTeamQueue');
  const teamQueue = createQueue('gameworldTeamQueue', teamDLQ);

  // Create fixture generation queue for direct access
  const fixtureGenerationDLQ = createDLQ('fixtureGeneration');
  const fixtureGenerationQueue = createQueue(
    'fixtureGeneration',
    fixtureGenerationDLQ,
    3, // maxReceiveCount - retry 3 times before going to DLQ
    {
      visibilityTimeout: 120,
    }
  );

  // Create league movement queue for reliable processing
  const leagueMovementDLQ = createDLQ('leagueMovement');
  const leagueMovementQueue = createQueue(
    'leagueMovement',
    leagueMovementDLQ,
    3, // maxReceiveCount - retry 3 times before going to DLQ
    {
      visibilityTimeout: 180, // 3 minutes for league movement processing
    }
  );

  // Add send permissions to the gameworld role for both queues
  let gameworldRole = addQueueSendPolicyToRole(
    'gameworldToUnattachedPlayersQueue',
    unattachedPlayersQueue
  );
  gameworldRole = addQueueSendPolicyToRole('gameworldToTeamQueue', teamQueue, gameworldRole);

  const [gameworldLambda, gameworldLogGroup] = createLambdaFunction(
    'generateGameworld',
    '../dist/generate/gameworld',
    'index.handler',
    {
      UNATTACHED_PLAYERS_QUEUE_URL: unattachedPlayersQueue.url,
      TEAM_QUEUE_URL: teamQueue.url,
    },
    gameworldRole,
    undefined,
    {
      memorySize: 512,
      timeout: 60,
    }
  );

  createLambdaErrorLogAlarm(
    gameworldLambda,
    {
      name: 'generate-gameworld',
      description: 'Alarm for error logs in generate gameworld Lambda function',
      alarmActions: [errorAlarmTopic.arn],
    },
    gameworldLogGroup
  );

  // End of season processing
  const endOfSeasonDLQ = createDLQ('endOfSeasonQueue');
  const endOfSeasonQueue = createQueue('endOfSeasonQueue', endOfSeasonDLQ, 1);

  let endOfSeasonRole = addQueueReadPolicyToRole('endOfSeasonQueue', endOfSeasonQueue);

  endOfSeasonRole = addQueueSendPolicyToRole(
    'processEndOfSeasonSendToGeneratePlayersQueue',
    generatePlayersQueue,
    endOfSeasonRole
  );

  const [endOfSeasonLambda, endOfSeasonLogGroup] = createLambdaFunction(
    'processEndOfSeason',
    '../dist/league/processEndOfSeason',
    'index.handler',
    {
      QUEUE_URL: endOfSeasonQueue.url,
      GENERATE_PLAYERS_QUEUE_URL: generatePlayersQueue.url,
    },
    endOfSeasonRole,
    undefined,
    {
      memorySize: 384,
      timeout: 900,
    }
  );

  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS('processEndOfSeason', endOfSeasonLambda, endOfSeasonQueue);

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'processEndOfSeason',
    endOfSeasonLambda,
    endOfSeasonQueue,
    endOfSeasonDLQ,
    10, // batchSize
    sqsBatchWindowMaximum, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  // Create role for processLeagueMovement with queue read permissions
  let processLeagueMovementRole = addQueueReadPolicyToRole(
    'processLeagueMovement',
    leagueMovementQueue
  );
  processLeagueMovementRole = addQueueSendPolicyToRole(
    'processLeagueMovement',
    fixtureGenerationQueue,
    processLeagueMovementRole
  );

  const [processLeagueMovementLambda, processLeagueMovementLogGroup] = createLambdaFunction(
    'processLeagueMovement',
    '../dist/gameworld/processLeagueMovement',
    'index.handler',
    { FIXTURE_GENERATION_QUEUE_URL: fixtureGenerationQueue.url },
    processLeagueMovementRole,
    undefined,
    {
      memorySize: 384,
      timeout: 900, // 15 minutes
    }
  );

  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS(
    'processLeagueMovement',
    processLeagueMovementLambda,
    leagueMovementQueue
  );

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'processLeagueMovement',
    processLeagueMovementLambda,
    leagueMovementQueue,
    leagueMovementDLQ,
    1, // batchSize - process one gameworld at a time
    0, // no batching window for immediate processing
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    },
    [errorAlarmTopic.arn]
  );

  let checkRole = addQueueSendPolicyToRole('checkForSeasonEnd', endOfSeasonQueue);
  // Add permission to send to league movement queue instead of direct lambda invoke
  checkRole = addQueueSendPolicyToRole(
    'checkForSeasonEndToLeagueMovementQueue',
    leagueMovementQueue,
    checkRole
  );

  const [checkForSeasonEnd, checkForSeasonEndLogGroup] = createLambdaFunction(
    'checkForSeasonEnd',
    '../dist/gameworld/checkForSeasonEnd',
    'index.handler',
    {
      QUEUE_URL: endOfSeasonQueue.url,
      LEAGUE_MOVEMENT_QUEUE_URL: leagueMovementQueue.url,
    },
    checkRole,
    undefined,
    {
      memorySize: 386,
      timeout: 30,
    }
  );

  createLambdaErrorLogAlarm(
    checkForSeasonEnd,
    {
      name: 'check-season-end',
      description: 'Alarm for error logs in check for season end Lambda function',
      alarmActions: [errorAlarmTopic.arn],
    },
    checkForSeasonEndLogGroup
  );

  // Create EventBridge rules to trigger the lambda at midnight UK time
  createScheduledRule({
    name: 'check-season-end',
    description: 'Triggers a lambda to see if any gameworlds season has ended',
    scheduleExpression: 'cron(0 0 * * ? *)', // Midnight daily
    lambda: checkForSeasonEnd,
  });

  const generatePlayersQueueRole = addQueueReadPolicyToRole(
    'generatePlayersQueue',
    generatePlayersQueue
  );

  const [generatePlayersLambda, generatePlayersLambdaLogGroup] = createLambdaFunction(
    'generatePlayersHandler',
    '../dist/generate/generate-players',
    'index.handler',
    {},
    generatePlayersQueueRole
  );

  // Create monitored event source mapping to connect the Lambda to the queue
  createMonitoredEventSourceMapping(
    'generatePlayers',
    generatePlayersLambda,
    generatePlayersQueue,
    generatePlayersDLQ,
    10, // batchSize
    sqsBatchWindowMinimum, // maximumBatchingWindowInSeconds
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
      scalingConfig: { maximumConcurrency: 3 },
    },
    [errorAlarmTopic.arn] // alarmActions
  );

  const [countAvailableTeamsLambda] = createLambdaFunction(
    'countAvailableTeamsHandler',
    '../dist/gameworld/countAvailableTeams',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
    }
  );

  let fixtureGenerationQueueRole = addQueueReadPolicyToRole(
    'processFixtureGenerationQueue',
    fixtureGenerationQueue
  );

  fixtureGenerationQueueRole = addDynamoPolicyToRole(
    'processFixtureGenerationQueue',
    [fixtureDetailTable],
    ['dynamodb:*'],
    fixtureGenerationQueueRole
  );

  const [processFixtureGenerationQueueLambda] = createLambdaFunction(
    'processFixtureGenerationQueue',
    '../dist/generate/processFixtureGenerationQueue',
    'index.handler',
    {
      FIXTURE_DETAIL_TABLE_NAME: fixtureDetailTable.name,
    },
    fixtureGenerationQueueRole,
    undefined,
    {
      memorySize: 256,
      timeout: 120, // 2 minutes for fixture generation processing
    }
  );

  addLambdaPermissionForSQS(
    'processFixtureGenerationQueue',
    processFixtureGenerationQueueLambda,
    fixtureGenerationQueue
  );

  createMonitoredEventSourceMapping(
    'processFixtureGenerationQueue',
    processFixtureGenerationQueueLambda,
    fixtureGenerationQueue,
    fixtureGenerationDLQ,
    5, // batchSize
    sqsBatchWindow, // default batch window 60s
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
      scalingConfig: {
        maximumConcurrency: 3, // Limit concurrency to avoid overwhelming the database
      },
    },
    [errorAlarmTopic.arn]
  );

  return {
    gameworldLambda,
    endOfSeasonLambda,
    endOfSeasonQueue,
    teamQueue,
    teamDLQ,
    unattachedPlayersQueue,
    unattachedPlayersDLQ,
    errorAlarmTopic,
    generatePlayersQueue,
    countAvailableTeamsLambda,
    fixtureGenerationQueue,
    leagueMovementQueue,
    leagueMovementDLQ,
  };
}
