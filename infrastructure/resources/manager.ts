import { Queue } from '@pulumi/aws/sqs';
import { sqsBatchWindowMinimum } from '../config';
import { createLambdaFunction } from '../lambda';
import {
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface ManagerResourcesConfig {
  playerQueue: Queue;
}

export function createManagerResources(config: ManagerResourcesConfig) {
  // Create manager queue
  const createManagerDlq = createDLQ('createManagerDLQ');
  const createManagerQueue = createQueue('createManager', createManagerDlq);

  // Post confirmation Lambda
  let createManagerQueueSendRole = addQueueSendPolicyToRole(
    'createManagerQueueSendRole',
    createManagerQueue
  );

  const [postConfirmationLambda] = createLambdaFunction(
    'postConfirmation',
    '../dist/manager/requestNewManagerOnSignup',
    'index.handler',
    {
      MANAGER_QUEUE_URL: createManagerQueue.url,
    },
    createManagerQueueSendRole
  );

  const [getManagerLambda] = createLambdaFunction(
    'getManagerHandler',
    '../dist/manager/getManager',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // Create manager Lambda to process queue messages
  let createManagerQueueReadRole = addQueueReadPolicyToRole(
    'createManagerQueueReadRole',
    createManagerQueue
  );

  const [createManagerLambda] = createLambdaFunction(
    'createManagerHandler',
    '../dist/manager/createAndAssignNewManager',
    'index.handler',
    {
      PLAYER_QUEUE_URL: config.playerQueue.url,
    },
    createManagerQueueReadRole,
    undefined,
    {
      memorySize: 256,
      timeout: 120,
    }
  );

  // Create event source mapping to connect the Lambda to the queue
  const createManagerEventSourceMapping = createMonitoredEventSourceMapping(
    'createManager',
    createManagerLambda,
    createManagerQueue,
    createManagerDlq,
    10, // batchSize
    sqsBatchWindowMinimum,
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    }
  );

  // Create update manager name Lambda
  const [updateManagerNameLambda] = createLambdaFunction(
    'updateManagerNameHandler',
    '../dist/manager/updateManagerName',
    'index.handler'
  );

  const [updateNotificationPreferencesLambda] = createLambdaFunction(
    'updateNotificationPreferencesHandler',
    '../dist/manager/updateNotificationPreferences',
    'index.handler'
  );

  // Create get inbox messages Lambda
  const [getInboxMessagesLambda] = createLambdaFunction(
    'getInboxMessagesHandler',
    '../dist/inbox/getInboxMessages',
    'index.handler'
  );

  const [getRewardsLambda] = createLambdaFunction(
    'getRewardsHandler',
    '../dist/manager/dailyReward',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  const [unsubscribeLambda] = createLambdaFunction(
    'unsubscribeHandler',
    '../dist/manager/unsubscribe',
    'index.handler'
  );

  return {
    postConfirmationLambda,
    getManagerLambda,
    createManagerLambda,
    createManagerEventSourceMapping,
    updateManagerNameLambda,
    updateNotificationPreferencesLambda,
    getInboxMessagesLambda,
    getRewardsLambda,
    unsubscribeLambda,
  };
}
