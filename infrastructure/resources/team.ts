import { Queue } from '@pulumi/aws/sqs';
import { sqsBatchWindowMinimum } from '../config';
import { createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface TeamResourcesConfig {
  teamQueue: Queue;
  teamDLQ: Queue;
  fixtureGenerationQueue: Queue;
}

export function createTeamResources(config: TeamResourcesConfig) {
  let teamQueueRole = addQueueReadPolicyToRole('teamQueue', config.teamQueue);

  // Create a player queue for direct access
  const playerDLQ = createDLQ('teamToPlayerQueue');
  const playerQueue = createQueue('teamToPlayerQueue', playerDLQ);

  // Add send permissions to the team role for the player queue
  teamQueueRole = addQueueSendPolicyToRole('teamToPlayerQueue', playerQueue, teamQueueRole);

  // Add permission for team lambda to invoke generate fixtures lambda
  let generateTeamRole = addQueueSendPolicyToRole(
    'teamToFixtureGenerationQueue',
    config.fixtureGenerationQueue,
    teamQueueRole
  );

  // Create environment variables object
  const envVars: { [key: string]: any } = {
    QUEUE_URL: config.teamQueue.url,
    PLAYER_QUEUE_URL: playerQueue.url,
    FIXTURE_GENERATION_QUEUE_URL: config.fixtureGenerationQueue.url,
  };

  const [generateTeamLambda] = createLambdaFunction(
    'generateTeamHandler',
    '../dist/generate/team',
    'index.handler',
    envVars,
    generateTeamRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  // Add Lambda Permission for SQS to invoke it
  addLambdaPermissionForSQS('generateTeamHandler', generateTeamLambda, config.teamQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('team-error');

  // Tell the lambda to monitor the queue for new messages
  createMonitoredEventSourceMapping(
    'generateTeamHandler',
    generateTeamLambda,
    config.teamQueue,
    config.teamDLQ,
    10, // batchSize
    sqsBatchWindowMinimum, // maximumBatchingWindowInSeconds
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    }, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const [getTeamLambda] = createLambdaFunction(
    'getTeamHandler',
    '../dist/team/getTeam',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  const [updateTeamOrderLambda] = createLambdaFunction(
    'updateTeamOrderHandler',
    '../dist/team/updateTeamOrder',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  const [upgradeTrainingLambda] = createLambdaFunction(
    'upgradeTrainingHandler',
    '../dist/team/upgradeTraining',
    'index.handler'
  );

  const [getTransactionsLambda] = createLambdaFunction(
    'getTransactionsHandler',
    '../dist/team/getTransactions',
    'index.handler',
    {},
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  const [updateTeamNameLambda] = createLambdaFunction(
    'updateTeamNameHandler',
    '../dist/team/updateTeamName',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  return {
    playerQueue,
    playerDLQ,
    generateTeamLambda,
    getTeamLambda,
    updateTeamOrderLambda,
    upgradeTrainingLambda,
    errorAlarmTopic,
    getTransactionsLambda,
    updateTeamNameLambda,
  };
}
