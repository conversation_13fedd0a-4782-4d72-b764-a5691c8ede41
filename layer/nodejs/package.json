{"name": "jfg-lambda-layer", "version": "1.0.0", "dependencies": {"@mikro-orm/core": "^6.4.15", "@mikro-orm/postgresql": "^6.4.15", "@mikro-orm/migrations": "^6.4.15", "@mikro-orm/reflection": "^6.4.15", "reflect-metadata": "^0.2.2", "pg": "^8.16.3", "pg-query-stream": "^4.8.1", "@aws-sdk/client-dynamodb": "^3.744.0", "@aws-sdk/client-ses": "^3.744.0", "@aws-sdk/client-sns": "^3.744.0", "@aws-sdk/client-sqs": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.744.0", "@aws-lambda-powertools/logger": "^2.19.1", "@aws-lambda-powertools/tracer": "^2.19.1", "@middy/core": "^6.0.0", "@middy/event-normalizer": "^6.0.0", "@middy/http-cors": "^6.0.0", "@middy/http-event-normalizer": "^6.0.0", "@middy/http-header-normalizer": "^6.0.0", "@middy/http-json-body-parser": "^6.0.0", "@middy/http-security-headers": "^6.0.0", "@middy/input-output-logger": "^6.0.0", "@middy/validator": "^6.0.0", "ajv": "^8.17.1", "aws-jwt-verify": "^5.0.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo-server-sdk": "^3.15.0", "google-auth-library": "^9.15.1", "obscenity": "^0.4.3", "seedrandom": "^3.0.5", "uuid": "^11.1.0"}}