import { defineConfig } from '@mikro-orm/core';
import { EntityGenerator } from '@mikro-orm/entity-generator';
import { Migrator } from '@mikro-orm/migrations';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import { TsMorphMetadataProvider } from '@mikro-orm/reflection';
import 'dotenv/config';
import path from 'path';
import { AppVersion } from './src/entities/AppVersion.js';
import { AvailableTeam } from './src/entities/AvailableTeam.js';
import { BidHistory } from './src/entities/BidHistory.js';
import { Fixture } from './src/entities/Fixture.js';
import { Gameworld } from './src/entities/Gameworld.js';
import { League } from './src/entities/League.js';
import { LeagueRules } from './src/entities/LeagueRules.js';
import { Manager } from './src/entities/Manager.js';
import { Player } from './src/entities/Player.js';
import { PlayerAttributes } from './src/entities/PlayerAttributes.js';
import { PlayerMatchHistory } from './src/entities/PlayerMatchHistory.js';
import { PlayerOverallStats } from './src/entities/PlayerOverallStats.js';
import { Purchases } from './src/entities/Purchases.js';
import { ScoutedPlayer } from './src/entities/ScoutedPlayer.js';
import { ScoutingRequest } from './src/entities/ScoutingRequest.js';
import { Team } from './src/entities/Team.js';
import { TeamTrainingSlot } from './src/entities/TeamTrainingSlot.js';
import { Transactions } from './src/entities/Transactions.js';
import { TransferListedPlayer } from './src/entities/TransferListedPlayer.js';
import { TransferRequest } from './src/entities/TransferRequest.js';

// Determine if we're running in AWS Lambda environment
const isLambda = !!process.env.AWS_LAMBDA_FUNCTION_NAME;

// Use /tmp directory in Lambda environment
const tempDir = isLambda ? '/tmp' : process.cwd() + '/temp';

export default defineConfig({
  entities: [
    AppVersion,
    AvailableTeam,
    BidHistory,
    Fixture,
    League,
    LeagueRules,
    Manager,
    PlayerAttributes,
    PlayerMatchHistory,
    PlayerOverallStats,
    Player,
    Purchases,
    ScoutedPlayer,
    ScoutingRequest,
    Gameworld,
    Team,
    TransferListedPlayer,
    TransferRequest,
    TeamTrainingSlot,
    Transactions,
  ],
  dbName: process.env.DATABASE_NAME || 'jfg',
  driver: PostgreSqlDriver,
  host: process.env.DATABASE_URL,
  port: Number(process.env.DATABASE_PORT) || 5432,
  user: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  debug: process.env.STAGE === 'dev',
  metadataProvider: TsMorphMetadataProvider,
  //logger: (msg) => logger.debug(msg),
  migrations: {
    path: path.join(process.cwd(), 'src/migrations'),
    glob: '!(*.d).{js,ts}',
  },
  discovery:
    process.env.STAGE === 'dev'
      ? {
          warnWhenNoEntities: true,
          requireEntitiesArray: true, // Always require explicit entity list
          alwaysAnalyseProperties: false, // Don't analyze properties when not needed
          disableDynamicFileAccess: isLambda, // Disable dynamic file access in Lambda
        }
      : {
          warnWhenNoEntities: false,
          requireEntitiesArray: true,
          alwaysAnalyseProperties: false,
          disableDynamicFileAccess: true, // Always disable for faster startup
        },
  strict: true,
  validate: true,
  extensions: [EntityGenerator, Migrator],
  // Use /tmp directory for temporary files in Lambda environment
  baseDir: isLambda ? tempDir : undefined,
  // Set metadata cache directory path for MikroORM
  metadataCache: {
    enabled: true,
    options: { cacheDir: tempDir, adapter: isLambda ? undefined : 'memory' },
  },
  forceEntityConstructor: true,
  validateRequired: false,
});
