#!/usr/bin/env node
/**
 * Interactive admin menu for managing the game database
 * Provides a user-friendly interface to access all admin tools
 *
 * Usage: npm run admin:menu
 */

import { spawn } from 'child_process';
import 'dotenv/config';

interface MenuOption {
  key: string;
  description: string;
  script?: string;
  requiresGameworldId?: boolean;
  optionalGameworldId?: boolean;
  requiresManagerId?: boolean;
  action?: () => Promise<void>;
}

const MENU_OPTIONS: MenuOption[] = [
  {
    key: '1',
    description: 'List all gameworlds',
    script: 'list-gameworlds.ts',
  },
  {
    key: '2',
    description: 'Show gameworld statistics',
    script: 'gameworld-stats.ts',
    requiresGameworldId: true,
  },
  {
    key: '3',
    description: 'List managers in gameworld',
    script: 'list-managers.ts',
    requiresGameworldId: true,
  },
  {
    key: '4',
    description: 'Delete gameworld and all data',
    script: 'delete-gameworld.ts',
    requiresGameworldId: true,
  },
  {
    key: '5',
    description: 'Delete manager',
    script: 'delete-manager.ts',
    requiresManagerId: true,
  },
  {
    key: '6',
    description: 'Reset player energy in gameworld',
    script: 'reset-player-energy.ts',
    requiresGameworldId: true,
  },
  {
    key: '7',
    description: 'Clean up duplicate fixtures (all gameworlds or specific)',
    script: 'cleanup-duplicate-fixtures.ts',
    optionalGameworldId: true,
  },
  {
    key: '8',
    description: 'Assign manager and team (removes team from available)',
    script: 'assign-manager.ts',
  },
  {
    key: 'q',
    description: 'Quit',
    action: async () => {
      console.log('Goodbye!');
      process.exit(0);
    },
  },
];

async function getInput(prompt: string): Promise<string> {
  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(prompt, (answer) => {
      rl.close();
      resolve(answer.trim());
    });
  });
}

function validateUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

async function runScript(scriptPath: string, args: string[] = []): Promise<void> {
  return new Promise((resolve, reject) => {
    const fullPath = `scripts/admin-tools/${scriptPath}`;
    const child = spawn('node', ['--loader', 'ts-node/esm', fullPath, ...args], {
      stdio: 'inherit',
      cwd: process.cwd(),
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Script exited with code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

function displayMenu() {
  console.log('\n=== JUMPERS FOR GOALPOSTS - ADMIN TOOLS ===\n');

  const dbName = process.env.DATABASE_NAME || 'Unknown';
  const dbHost = process.env.DATABASE_URL || 'Unknown';
  console.log(`Database: ${dbName} @ ${dbHost}\n`);

  MENU_OPTIONS.forEach((option) => {
    console.log(`${option.key}. ${option.description}`);
  });

  console.log('');
}

async function handleMenuSelection(selection: string): Promise<boolean> {
  const option = MENU_OPTIONS.find((opt) => opt.key === selection);

  if (!option) {
    console.log('Invalid selection. Please try again.');
    return true;
  }

  if (option.action) {
    await option.action();
    return false;
  }

  if (!option.script) {
    console.log('No script defined for this option.');
    return true;
  }

  const args: string[] = [];

  // Get required inputs
  if (option.requiresGameworldId) {
    while (true) {
      const gameworldId = await getInput('Enter gameworld ID: ');
      if (!gameworldId) {
        console.log('Gameworld ID is required.');
        continue;
      }
      if (!validateUUID(gameworldId)) {
        console.log('Invalid UUID format. Please enter a valid gameworld ID.');
        continue;
      }
      args.push(gameworldId);
      break;
    }
  }

  // Get optional inputs
  if (option.optionalGameworldId) {
    const gameworldId = await getInput(
      'Enter gameworld ID (optional, press Enter for all gameworlds): '
    );
    if (gameworldId) {
      if (!validateUUID(gameworldId)) {
        console.log('Invalid UUID format. Please enter a valid gameworld ID.');
        return true;
      }
      args.push(gameworldId);
    }
  }

  if (option.requiresManagerId) {
    while (true) {
      const managerId = await getInput('Enter manager ID: ');
      if (!managerId) {
        console.log('Manager ID is required.');
        continue;
      }
      if (!validateUUID(managerId)) {
        console.log('Invalid UUID format. Please enter a valid manager ID.');
        continue;
      }
      args.push(managerId);
      break;
    }
  }

  try {
    console.log(`\nRunning ${option.description.toLowerCase()}...\n`);
    await runScript(option.script, args);
    console.log('\nOperation completed. Press Enter to continue...');
    await getInput('');
  } catch (error) {
    console.error(`\nError running script: ${error}`);
    console.log('Press Enter to continue...');
    await getInput('');
  }

  return true;
}

async function main() {
  console.log('Initializing admin tools...');

  // Check required environment variables
  const requiredEnvVars = ['DATABASE_NAME', 'DATABASE_URL', 'DATABASE_USER', 'DATABASE_PASSWORD'];
  const missingEnvVars = requiredEnvVars.filter((varName) => !process.env[varName]);

  if (missingEnvVars.length > 0) {
    console.error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    console.error('Please ensure your .env file is properly configured.');
    process.exit(1);
  }

  let continueMenu = true;

  while (continueMenu) {
    displayMenu();
    const selection = await getInput('Select an option: ');
    continueMenu = await handleMenuSelection(selection);
  }
}

// Execute the main function
main().catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
