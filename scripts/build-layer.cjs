const esbuild = require('esbuild');
const fs = require('fs');
const path = require('path');

const rootDir = path.join(__dirname, '..');
const layerDir = path.join(rootDir, 'layer');
const nodeModulesDir = path.join(layerDir, 'nodejs', 'node_modules');

// Dependencies that should be included in the lambda layer
const layerDependencies = [
  // MikroORM and related
  '@mikro-orm/core',
  '@mikro-orm/postgresql',
  '@mikro-orm/migrations',
  '@mikro-orm/reflection',
  'reflect-metadata',
  'pg',
  'pg-query-stream',
  
  // AWS SDK (though Lambda runtime provides v3, we want specific versions)
  '@aws-sdk/client-dynamodb',
  '@aws-sdk/client-ses',
  '@aws-sdk/client-sns',
  '@aws-sdk/client-sqs',
  '@aws-sdk/lib-dynamodb',
  
  // AWS Lambda Powertools
  '@aws-lambda-powertools/logger',
  '@aws-lambda-powertools/tracer',
  
  // Middy middleware
  '@middy/core',
  '@middy/event-normalizer',
  '@middy/http-cors',
  '@middy/http-event-normalizer',
  '@middy/http-header-normalizer',
  '@middy/http-json-body-parser',
  '@middy/http-security-headers',
  '@middy/input-output-logger',
  '@middy/validator',
  
  // Other heavy dependencies
  'ajv',
  'aws-jwt-verify',
  'axios',
  'date-fns',
  'expo-server-sdk',
  'google-auth-library',
  'obscenity',
  'seedrandom',
  'uuid'
];

async function buildLayer() {
  console.log('Building Lambda Layer...');
  
  // Clean and create layer directory
  if (fs.existsSync(layerDir)) {
    fs.rmSync(layerDir, { recursive: true, force: true });
  }
  fs.mkdirSync(nodeModulesDir, { recursive: true });
  
  // Create a temporary package.json for the layer
  const layerPackageJson = {
    name: 'jfg-lambda-layer',
    version: '1.0.0',
    dependencies: {}
  };
  
  // Read the main package.json to get dependency versions
  const mainPackageJson = JSON.parse(fs.readFileSync(path.join(rootDir, 'package.json'), 'utf8'));
  
  // Add layer dependencies with their versions
  layerDependencies.forEach(dep => {
    if (mainPackageJson.dependencies[dep]) {
      layerPackageJson.dependencies[dep] = mainPackageJson.dependencies[dep];
    } else {
      console.warn(`Dependency ${dep} not found in main package.json`);
    }
  });
  
  // Write layer package.json
  fs.writeFileSync(
    path.join(layerDir, 'nodejs', 'package.json'),
    JSON.stringify(layerPackageJson, null, 2)
  );
  
  console.log('Layer package.json created with dependencies:', Object.keys(layerPackageJson.dependencies));
  
  // Create a simple script to install dependencies in the layer
  const installScript = `
cd "${path.join(layerDir, 'nodejs')}"
npm install --production --no-package-lock --no-audit --no-fund
`;
  
  fs.writeFileSync(path.join(layerDir, 'install.sh'), installScript);
  fs.writeFileSync(path.join(layerDir, 'install.bat'), installScript.replace(/\//g, '\\'));
  
  console.log('Layer build process completed. Run install.bat to install dependencies.');
  console.log('Layer directory:', layerDir);
}

// Run if called directly
if (require.main === module) {
  buildLayer().catch(console.error);
}

module.exports = { buildLayer, layerDependencies };
