/**
 * Inbox message entity for DynamoDB storage
 * Stores messages for teams within gameworlds
 */
export class Inbox {
  id!: string; // unique inbox message ID
  gameworldId!: string; // partition key component
  teamId!: string; // partition key component
  date!: number; // time in millis when the message was sent
  message!: string; // message to be sent to the user
  extra!: string; // extra information to be sent to the user
}
