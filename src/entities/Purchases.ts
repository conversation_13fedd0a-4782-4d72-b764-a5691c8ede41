import { Entity, Enum, ManyToOne, PrimaryKey, Property, type Rel, Unique } from '@mikro-orm/core';
import { Manager } from './Manager.js';

export enum PurchaseStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
  PAUSED = 'PAUSED',
  REFUNDED = 'REFUNDED',
  BILLING_ISSUE = 'BILLING_ISSUE',
}

export enum PurchaseType {
  SUBSCRIPTION = 'SUBSCRIPTION',
  NON_RENEWING = 'NON_RENEWING',
  CONSUMABLE = 'CONSUMABLE',
}

@Entity({ tableName: 'purchases' })
@Unique({ name: 'purchases_transaction_id_key', properties: ['transactionId'] })
export class Purchases {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @ManyToOne({ entity: () => Manager, fieldName: 'manager_id' })
  manager!: Rel<Manager>;

  @Property({ type: 'string' })
  productId!: string;

  @Property({ type: 'string' })
  transactionId!: string;

  @Property({ type: 'string' })
  originalTransactionId!: string;

  @Property({ type: 'string' })
  revenueCatEventId!: string;

  @Property({ type: 'string' })
  store!: string; // APP_STORE, PLAY_STORE, etc.

  @Property({ type: 'string' })
  environment!: string; // PRODUCTION, SANDBOX

  @Enum({ items: () => PurchaseType })
  purchaseType!: PurchaseType;

  @Enum({ items: () => PurchaseStatus })
  status!: PurchaseStatus;

  @Property({ type: 'bigint' })
  purchasedAt!: number; // timestamp in ms

  @Property({ type: 'bigint', nullable: true })
  expirationAt?: number; // timestamp in ms, null for non-renewing

  @Property({ type: 'string' })
  currency!: string;

  @Property({ type: 'numeric', precision: 10, scale: 2 })
  price!: number;

  @Property({ type: 'numeric', precision: 10, scale: 2 })
  priceInPurchasedCurrency!: number;

  @Property({ type: 'string', nullable: true })
  countryCode?: string;

  @Property({ type: 'jsonb', nullable: true })
  entitlementIds?: string[];

  @Property({ type: 'string', nullable: true })
  cancelReason?: string;

  @Property({ type: 'string', nullable: true })
  expirationReason?: string;

  @Property({ type: 'bigint' })
  createdAt!: number;

  @Property({ type: 'bigint' })
  updatedAt!: number;

  @Property({ type: 'jsonb', nullable: true })
  rawEventData?: any; // Store the full RevenueCat event for debugging

  constructor(data?: Partial<Purchases>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}
