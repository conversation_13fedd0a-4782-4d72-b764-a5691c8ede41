import { <PERSON><PERSON>ty, <PERSON>To<PERSON>ne, Primary<PERSON>ey, Property, type Rel } from '@mikro-orm/core';
import { Player } from './Player.js';
import { Team } from './Team.js';

@Entity({ tableName: 'team_training_slots' })
export class TeamTrainingSlot {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @ManyToOne(() => Team)
  team!: Rel<Team>;

  @Property()
  slotIndex!: number; // 0-4

  @ManyToOne(() => Player, { nullable: true })
  player?: Rel<Player>;

  @Property({ nullable: true })
  attribute?: string; // e.g. 'finishing', 'pace', etc.

  @Property({ nullable: true })
  startValue?: number; // attribute value when assigned

  @Property({ type: 'bigint', nullable: true })
  assignedAt?: number; // timestamp when training started
}
