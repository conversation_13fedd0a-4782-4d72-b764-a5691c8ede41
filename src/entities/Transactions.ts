import { <PERSON><PERSON>ty, ManyToOne, PrimaryKey, Property, type Rel } from '@mikro-orm/core';
import { Team } from './Team.js';

@Entity({ tableName: 'transactions' })
export class Transactions {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string; // unique transaction ID

  @Property({ type: 'uuid' })
  gameworldId!: string;

  @ManyToOne({ entity: () => Team, fieldName: 'team_id' })
  team!: Rel<Team>;

  @Property({ type: 'bigint' })
  date!: number; // time in millis when the transaction was made

  @Property({ type: 'numeric', precision: 15, scale: 2 })
  amount!: number; // amount of the transaction

  @Property({ type: 'text' })
  type!: string; // type of transaction (e.g. 'transfer', 'scouting', 'training', etc.)

  @Property({ type: 'jsonb' })
  details!: string; // details of the transaction

  constructor(data?: Partial<Transactions>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}
