import {
  Cascade,
  Collection,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryKey,
  Property,
  type Rel,
  Unique,
} from '@mikro-orm/core';
import { BidHistory } from './BidHistory.js';
import { Player } from './Player.js';

@Entity({ tableName: 'transfer_list' })
export class TransferListedPlayer {
  @PrimaryKey({ type: 'uuid', defaultRaw: 'uuid_generate_v4()' })
  id!: string;

  @Property({ type: 'uuid' })
  @Index() // Add index for faster lookups
  gameworldId!: string;

  @ManyToOne(() => Player, { cascade: [Cascade.PERSIST] }) // Avoid player being deleted when deleting transfer listed players
  @Unique({ properties: ['player', 'gameworldId'] })
  player!: Rel<Player>;

  @Property({ type: 'numeric', precision: 15, scale: 2 })
  auctionStartPrice!: number;

  @Property({ type: 'numeric', precision: 15, scale: 2 })
  auctionCurrentPrice!: number;

  @Property({ type: 'bigint' })
  @Index() // Add index for faster queries on auction end time
  auctionEndTime!: number;

  @Property({ type: 'int' })
  auctionListingCounter: number = 0;

  @Property({ type: 'bigint' })
  @Index()
  createdAt: number = Date.now();

  @OneToMany(() => BidHistory, (bidHistory) => bidHistory.transferListing, {
    orphanRemoval: true, // Automatically remove bids when a listing is removed
    cascade: [Cascade.ALL], // Cascade all operations to bids
  })
  bidHistory = new Collection<BidHistory>(this);

  // Helper method to get the highest bid
  getHighestBid(): BidHistory | null {
    const bids = this.bidHistory.getItems();
    if (bids.length === 0) return null;

    return bids.sort((a, b) => b.maximumBid - a.maximumBid)[0] || null;
  }

  // Helper method to get the winning bid based on current price
  getWinningBid(): { bid: BidHistory; bidAmount: number } | null {
    const bids = this.bidHistory.getItems().sort((a, b) => b.maximumBid - a.maximumBid);

    for (let i = 0; i < bids.length; i++) {
      const bid = bids[i]!;
      const bidAmount = i === 0 ? this.auctionCurrentPrice : bid.maximumBid;
      if (bid.team.balance >= bidAmount) {
        return { bid, bidAmount };
      }
    }
    return null;
  }

  // Helper method to check if auction has ended
  isExpired(): boolean {
    return this.auctionEndTime < Date.now();
  }
}
