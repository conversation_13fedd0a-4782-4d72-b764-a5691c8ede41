import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import axios from 'axios';

interface FeedbackBody {
  type: 'bug' | 'feature' | 'feedback';
  title: string;
  description: string;
  metadata?: Record<string, any>;
}

export type FeedbackEvent = HttpEvent<FeedbackBody, void, void>;

const NOTION_API_URL = 'https://api.notion.com/v1/pages';
const NOTION_VERSION = '2022-06-28';

const createNotionPage = async (body: FeedbackBody, userId: string) => {
  // Format the database ID properly (add hyphens if missing)
  const formatDatabaseId = (id: string) => {
    // Remove any existing hyphens first
    const cleanId = id.replace(/-/g, '');
    if (cleanId.length === 32) {
      return `${cleanId.slice(0, 8)}-${cleanId.slice(8, 12)}-${cleanId.slice(12, 16)}-${cleanId.slice(16, 20)}-${cleanId.slice(20)}`;
    }
    return id;
  };

  const formattedDbId = formatDatabaseId(process.env.NOTION_ISSUE_TRACKER_DB_ID!);

  // Map type to category
  const getCategoryFromType = (type: string): string => {
    switch (type) {
      case 'bug':
        return 'User Bug';
      case 'feedback':
        return 'User Feedback';
      case 'feature':
        return 'Feature Request';
      default:
        return 'User Feedback';
    }
  };

  const properties: Record<string, any> = {
    // Main title property
    Issue: {
      title: [
        {
          text: {
            content: body.title,
          },
        },
      ],
    },
    // Set status to "Backlog" by default
    Status: {
      status: {
        name: 'Backlog',
      },
    },
    // Set priority to "Untriaged"
    Priority: {
      select: {
        name: 'Untriaged',
      },
    },
    // Set category based on type
    Category: {
      multi_select: [
        {
          name: getCategoryFromType(body.type),
        },
      ],
    },
  };

  // Create the page body content
  const children = [];

  if (body.description) {
    children.push({
      object: 'block',
      type: 'paragraph',
      paragraph: {
        rich_text: [
          {
            type: 'text',
            text: {
              content: body.description,
            },
          },
        ],
      },
    });
  }

  if (userId) {
    children.push({
      object: 'block',
      type: 'paragraph',
      paragraph: {
        rich_text: [
          {
            type: 'text',
            text: {
              content: `Submitted by: ${userId}`,
            },
          },
        ],
      },
    });
  }

  if (body.metadata) {
    children.push({
      object: 'block',
      type: 'code',
      code: {
        language: 'json',
        rich_text: [
          {
            type: 'text',
            text: {
              content: JSON.stringify(body.metadata, null, 2),
            },
          },
        ],
      },
    });
  }

  const notionBody = {
    parent: { database_id: formattedDbId },
    properties,
    children: children,
  };

  console.log('Sending to Notion:', JSON.stringify(notionBody, null, 2));

  try {
    const response = await axios.post(NOTION_API_URL, notionBody, {
      headers: {
        Authorization: `Bearer ${process.env.NOTION_API_KEY!}`,
        'Notion-Version': NOTION_VERSION,
        'Content-Type': 'application/json',
      },
    });

    console.log('Notion page created:', response.data.id);
    return response.data;
  } catch (error: any) {
    console.error('Notion API error:', error.response?.data || error.message);
    throw new Error(
      `Notion API error (${error.response?.status}): ${error.response?.data || error.message}`
    );
  }
};

const main = async function (event: FeedbackEvent) {
  const { type, title, description, metadata } = event.body || {};

  // Validation
  if (!type || !['bug', 'feature', 'feedback'].includes(type)) {
    return buildResponse(
      400,
      JSON.stringify({ error: 'Type must be "bug", "feature", or "feedback"' })
    );
  }
  if (!title || title.trim().length < 3) {
    return buildResponse(400, JSON.stringify({ error: 'Title must be at least 3 characters' }));
  }
  if (!description || description.trim().length < 10) {
    return buildResponse(
      400,
      JSON.stringify({ error: 'Description must be at least 10 characters' })
    );
  }

  const userId = getUser(event);

  if (!userId) {
    return buildResponse(401, JSON.stringify({ error: 'User authentication required' }));
  }

  try {
    const result = await createNotionPage({ type, title, description, metadata }, userId);
    return buildResponse(
      200,
      JSON.stringify({
        message: 'Feedback submitted successfully',
        notionPageId: result.id,
      })
    );
  } catch (error: any) {
    console.error('Error creating Notion page:', error);
    return buildResponse(
      500,
      JSON.stringify({ error: 'Failed to submit feedback', details: error.message })
    );
  }
};

export const handler = httpMiddify(main, { injectRepositories: false });
