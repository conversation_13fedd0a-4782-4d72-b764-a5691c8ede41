import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Manager } from '@/entities/Manager.js';
import { Repositories } from '@/middleware/database/types.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { TransactionService } from '@/services/database/transaction-service.ts';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.ts';
import { logger } from '@/utils/logger.js';

export async function createAndAssignNewManager(
  managerId: string,
  repositories: Repositories,
  transactionService: TransactionService,
  email?: string
): Promise<{ manager: Manager; team: AvailableTeam }> {
  const existingManager = await repositories.managerRepository.getManagerById(managerId);
  if (existingManager) {
    logger.error('Manager already exists', { existingManager });
    return {
      manager: existingManager,
      team: {
        id: '',
        gameworldId: existingManager.team?.gameworldId!,
        teamId: existingManager.team?.teamId!,
      },
    };
  }
  try {
    return await transactionService.executeInTransaction(async (txEm) => {
      // Get a team from the available teams table
      logger.debug('Getting team');
      const team = await repositories.teamRepository.getAndDeleteAvailableTeam(txEm);
      if (!team) {
        throw new Error('No available teams');
      }
      logger.debug('Got team', { team });
      const teamId = team.teamId;
      const gameworldId = team.gameworldId;

      // Create a new manager
      const manager = new Manager();
      manager.managerId = managerId;
      manager.team = repositories.teamRepository.createFromPK(teamId);
      manager.email = email;
      manager.gameworldId = gameworldId;
      manager.scoutTokens = 3;
      manager.superScoutTokens = 0;
      manager.createdAt = new Date().getTime();
      manager.lastActive = new Date().getTime();

      // Save the manager
      logger.debug('Saving manager', { manager });
      await repositories.managerRepository.createManager(manager, txEm);

      // Make sure the team is correctly initialised
      await repositories.teamRepository.reinitialiseTeam(
        teamId,
        gameworldId,
        repositories.playerRepository,
        repositories.transferRepository,
        txEm
      );

      logger.debug('Manager saved');
      return { manager, team };
    });
  } catch (error) {
    logger.error('Error saving user:', { error });
    throw error;
  }
}

/**
 * Create a manager and assign a team to them
 * @param event
 */
async function main(event: HttpEvent<void, void, void>) {
  const userId = getUser(event);

  const { managerRepository } = event.context.repositories;
  const existingManager = await managerRepository.getManagerById(userId);
  if (existingManager) {
    logger.error('Manager already exists', { existingManager });
    return buildResponse(208, JSON.stringify({ status: 'Manager already exists' }));
  }

  // Get the user email from authenticator context
  const email = event.requestContext.authorizer?.email;

  const mikroOrmService = await getMikroOrmService();
  const transactionService = new TransactionService(mikroOrmService);

  // Process each manager creation
  const { team } = await createAndAssignNewManager(
    userId,
    event.context.repositories,
    transactionService,
    email
  );
  return buildResponse(
    201,
    JSON.stringify({
      managerId: userId,
      team: {
        teamId: team.teamId,
        gameworldId: team.gameworldId,
      },
    })
  );
}

export const handler = httpMiddify(main, {});
