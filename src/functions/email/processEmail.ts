import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { sendTemplatedEmail } from '@/services/email/index.js';
import { EmailEvent } from '@/types/generated/email-event.js';
import { logger } from '@/utils/logger.js';

const main = async function (event: SQSEvent<EmailEvent>): Promise<void> {
  for (const record of event.Records) {
    const emailEvent = record.body;
    try {
      logger.debug('Processing email request', {
        recipients: emailEvent.recipients,
        subject: emailEvent.subject,
      });

      let baseUrl;
      switch (process.env.STAGE) {
        case 'dev':
          baseUrl = 'https://jfg-stage.rwscripts.com';
          break;
        case 'stage':
          baseUrl = 'https://jfg-stage.rwscripts.com';
          break;
        case 'prod':
          baseUrl = 'https://jfg.rwscripts.com';
          break;
      }

      await sendTemplatedEmail(
        emailEvent.template || 'basic',
        emailEvent.recipients,
        emailEvent.subject,
        emailEvent.content,
        emailEvent.title,
        `${baseUrl}/manager/unsubscribe?id=${emailEvent.managerId}&category=${emailEvent.category}`,
        '<EMAIL>'
      );

      logger.debug('Email sent successfully', {
        recipients: emailEvent.recipients,
        subject: emailEvent.subject,
      });
    } catch (error) {
      logger.error('Failed to send email', { error, emailEvent });
      throw error; // Let SQS retry
    }
  }
};

export const handler = sqsMiddify<EmailEvent>(main, { injectRepositories: false });
