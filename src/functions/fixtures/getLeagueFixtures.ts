import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';

interface PathParameters {
  gameworldId: string;
  leagueId: string;
}

export type GetLeagueFixturesEvent = HttpEvent<void, PathParameters, void>;

const main = async function (event: GetLeagueFixturesEvent) {
  const { fixtureRepository } = event.context.repositories;

  const fixtures = await fixtureRepository.getFixturesByLeague(
    event.pathParameters.gameworldId,
    event.pathParameters.leagueId
  );

  if (!fixtures) {
    return buildResponse(404, JSON.stringify({ error: 'Fixtures not found' }));
  }

  const response = {
    fixtures,
  };

  return buildResponse(200, JSON.stringify(response));
};

export const handler = httpMiddify(main, {});
