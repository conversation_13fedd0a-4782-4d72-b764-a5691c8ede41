import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { APIGatewayProxyResult } from 'aws-lambda';
import { readFileSync } from 'fs';
import { join } from 'path';

const main = function (_: HttpEvent<void, void, void>): Promise<APIGatewayProxyResult> {
  const csv = readFileSync(join(__dirname, 'commentary.csv'), 'utf8');
  // convert csv to json, skip header row and remove carriage returns
  const result = csv
    .split('\n')
    .slice(1) // Skip header row
    .map((line) => line.split(','))
    .reduce(
      (acc, [id, text]) => {
        if (id && text) {
          // Only process if both id and text exist
          acc[id.trim()] = text.trim(); // <PERSON><PERSON> removes both \r and whitespace
        }
        return acc;
      },
      {} as Record<string, string>
    );

  return Promise.resolve(buildResponse(200, JSON.stringify(result)));
};

export const handler = httpMiddify(main, {});
