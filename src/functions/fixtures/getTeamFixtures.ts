import { Scorer } from '@/entities/types.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';

interface PathParameters {
  gameworldId: string;
  leagueId: string;
  teamId: string;
}

interface GetTeamFixturesResponse {
  fixtures: {
    fixtureId: string;
    homeTeamId: string;
    homeTeamName: string;
    awayTeamId: string;
    awayTeamName: string;
    date: number;
    score?: [number, number];
    scorers?: Scorer[];
    played: boolean;
  }[];
}

export type GetTeamFixturesEvent = HttpEvent<void, PathParameters, void>;

const main = async function (event: GetTeamFixturesEvent) {
  const { fixtureRepository } = event.context.repositories;

  // filter only fixtures where the team is playing home or away
  const teamFixtures = await fixtureRepository.getFixturesByTeam(
    event.pathParameters.gameworldId,
    event.pathParameters.teamId
  );

  logger.debug('Retrieved team fixtures', {
    teamId: event.pathParameters.teamId,
    fixture: teamFixtures[0],
  });

  const response: GetTeamFixturesResponse = {
    fixtures: teamFixtures.map((fixture) => ({
      fixtureId: fixture.fixtureId,
      homeTeamId: fixture.homeTeam.teamId,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      homeTeamName: fixture.homeTeam.teamName,
      awayTeamId: fixture.awayTeam.teamId,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      awayTeamName: fixture.awayTeam.teamName,
      date: fixture.date,
      score: fixture.score,
      scorers: fixture.scorers,
      played: fixture.played,
    })),
  };

  return buildResponse(200, JSON.stringify(response));
};

export const handler = httpMiddify(main, {});
