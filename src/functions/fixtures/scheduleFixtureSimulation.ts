import { sendFixturesToQueue } from '@/functions/fixtures/fixtureSimulationUtils.js';
import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler, EventWithRepositories } from '@/middleware/event/types.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';

/**
 * This lambda is triggered by EventBridge at 10am and 8pm UK time every day
 * It finds all unplayed fixtures with dates in the past and sends them to SQS for simulation
 */
const main: EventHandler<EventWithRepositories, void> = async function (event): Promise<void> {
  const { fixtureRepository } = event.context.repositories;

  logger.debug('Starting scheduled fixture simulation');

  // Fetch all due fixtures in pages of 300
  const pageSize = 300;
  let offset = 0;
  let page: SimulateFixturesEvent[] = [];
  let count = 0;
  do {
    page = await fixtureRepository.getDueFixtures(undefined, undefined, pageSize, offset);
    count += page.length;
    if (page.length === 0) {
      break;
    }

    // Send fixtures to SQS for simulation
    await sendFixturesToQueue(page);

    offset += pageSize;
  } while (page.length === pageSize);

  logger.info(`Successfully queued ${count} fixtures for simulation`);
};

export const handler = eventMiddify(main);
