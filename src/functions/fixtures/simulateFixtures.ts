import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TransactionService } from '@/services/database/transaction-service.js';
import { FixtureDatabaseService } from '@/services/fixtures/fixture-database-service.js';
import { FixtureSimulationService } from '@/services/fixtures/fixture-simulation-service.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';

/**
 * Process a single fixture simulation within a database transaction
 */
async function processFixture(
  fixture: SimulateFixturesEvent,
  transactionService: TransactionService,
  simulationService: FixtureSimulationService,
  databaseService: FixtureDatabaseService
): Promise<void> {
  const correlationId = `fixture-${fixture.fixtureId}-${Date.now()}`;

  logger.debug('Processing fixture simulation', {
    fixtureId: fixture.fixtureId,
    correlationId,
  });

  // 1. Simulate the fixture first (outside transaction)
  const simulationData = await simulationService.simulateFixture(fixture);

  // 2. Execute all database updates within a transaction
  await transactionService.executeInTransaction(async (em) => {
    try {
      await databaseService.updateFixtureResults(
        em,
        simulationData.fixture.fixtureId,
        simulationData.fixture.gameworldId,
        simulationData.fixture.leagueId,
        simulationData.seed,
        simulationData.result.stats,
        simulationData.homeTeam,
        simulationData.awayTeam,
        simulationData.homeTeamConverted,
        simulationData.awayTeamConverted,
        simulationData.result,
        simulationData.fixture.fixtureDate
      );
    } catch (error) {
      logger.error('Failed to update fixture results in transaction', {
        fixtureId: fixture.fixtureId,
        correlationId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  });

  // 3. Handle post-transaction operations
  try {
    // Store fixture details in DynamoDB if there's a human manager
    if (simulationData.homeTeam.manager || simulationData.awayTeam.manager) {
      await databaseService.storeFixtureDetails(
        simulationData.fixture.fixtureId,
        simulationData.fixture.gameworldId,
        simulationData.fixture.leagueId,
        simulationData.homeTeam.teamId,
        simulationData.homeTeam.teamName,
        simulationData.awayTeam.teamId,
        simulationData.awayTeam.teamName,
        Date.now(), // Use current time as fixture date
        simulationData.result.stats,
        simulationData.result.events,
        simulationData.homeTeamConverted.players,
        simulationData.awayTeamConverted.players
      );
    }

    // Send notifications
    await simulationService.sendMatchNotifications(
      simulationData.homeTeam,
      simulationData.awayTeam,
      simulationData.result
    );

    logger.debug('Fixture simulation completed successfully', {
      fixtureId: fixture.fixtureId,
      correlationId,
      homeScore: simulationData.result.stats.score[0],
      awayScore: simulationData.result.stats.score[1],
    });
  } catch (error) {
    // Log but don't fail the entire operation for post-transaction errors
    logger.warn('Post-transaction operations failed', {
      fixtureId: fixture.fixtureId,
      correlationId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

const main = async function (event: SQSEvent<SimulateFixturesEvent>): Promise<{
  batchItemFailures: { itemIdentifier: string }[];
}> {
  logger.debug('Starting fixture simulation batch processing');

  const { repositories } = event.context;
  const batchItemFailures: { itemIdentifier: string }[] = [];

  // Initialize services
  const mikroOrmService = await getMikroOrmService();
  const transactionService = new TransactionService(mikroOrmService);
  const simulationService = new FixtureSimulationService(repositories);
  const databaseService = new FixtureDatabaseService(repositories);

  // Process each fixture in the batch
  for (const record of event.Records) {
    try {
      const fixture = record.body;

      await processFixture(fixture, transactionService, simulationService, databaseService);
    } catch (error) {
      logger.error('Failed to simulate fixture', {
        error: error instanceof Error ? error.message : 'Unknown error',
        messageId: record.messageId,
        body: record.body,
        stack: error instanceof Error ? error.stack : undefined,
      });

      batchItemFailures.push({ itemIdentifier: record.messageId });
    }
  }

  logger.debug('Fixture simulation batch processing completed', {
    totalRecords: event.Records.length,
    failures: batchItemFailures.length,
    successCount: event.Records.length - batchItemFailures.length,
  });

  return { batchItemFailures };
};

export const handler = sqsMiddify<SimulateFixturesEvent>(main, {});
