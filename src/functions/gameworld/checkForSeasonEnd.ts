import { eventMiddify } from '@/middleware/event/index.js';
import { <PERSON><PERSON><PERSON><PERSON>, EventWithRepositories } from '@/middleware/event/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { EndOfSeasonEvent } from '@/types/generated/end-of-season-event.js';
import { LeagueMovementEvent } from '@/types/generated/league-movement-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { SendMessageBatchRequestEntry } from '@aws-sdk/client-sqs/dist-types/models/models_0.js';

const sqs = new SQS({ tracer });

/**
 * This lambda is triggered by EventBridge at midnight UK time every day
 * It finds all unplayed fixtures with dates in the past and sends them to SQS for simulation
 */
const main: EventHandler<EventWithRepositories, void> = async function (event): Promise<void> {
  const { gameworldRepository, leagueRepository } = event.context.repositories;

  logger.debug('Checking gameworld end dates');

  // Find all fixtures that are due but not played yet
  const completedGameworlds = await gameworldRepository.getCompletedSeasons();

  if (completedGameworlds.length === 0) {
    logger.info('No gameworlds complete');
    return;
  }

  const sqsPayload: SendMessageBatchRequestEntry[] = [];
  for (const completedGameworld of completedGameworlds) {
    const leagues = await leagueRepository.getLeaguesByGameworld(completedGameworld.id, false);

    for (const league of leagues) {
      if (league.tier === 1) {
        sqsPayload.push({
          Id: completedGameworld.id,
          MessageBody: JSON.stringify({
            gameworldId: completedGameworld.id,
            leagueId: league.id,
          } as EndOfSeasonEvent),
        });
      }
    }
  }

  if (!process.env.QUEUE_URL) {
    throw new Error('QUEUE_URL environment variable not set');
  }
  logger.info(
    `Queueing ${sqsPayload.length} leagues from ${completedGameworlds.length} gameworlds for processing`
  );

  await sqs.sendBatch(process.env.QUEUE_URL!, sqsPayload);

  if (!process.env.LEAGUE_MOVEMENT_QUEUE_URL) {
    throw new Error('LEAGUE_MOVEMENT_QUEUE_URL environment variable not set');
  }

  // Send league movement messages to queue for reliable processing
  const leagueMovementPayload: SendMessageBatchRequestEntry[] = [];
  for (const completedGameworld of completedGameworlds) {
    leagueMovementPayload.push({
      Id: `league-movement-${completedGameworld.id}`,
      MessageBody: JSON.stringify({
        gameworldId: completedGameworld.id,
      } as LeagueMovementEvent),
    });
  }

  if (leagueMovementPayload.length > 0) {
    logger.info(
      `Queueing ${leagueMovementPayload.length} league movement tasks for processing`
    );
    await sqs.sendBatch(process.env.LEAGUE_MOVEMENT_QUEUE_URL!, leagueMovementPayload);
  }
};

export const handler = eventMiddify(main);
