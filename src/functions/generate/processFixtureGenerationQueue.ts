import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { TransactionService } from '@/services/database/transaction-service.js';
import { FixtureGenerationDatabaseService } from '@/services/fixtures/fixture-generation-database-service.js';
import { FixtureGenerationService } from '@/services/fixtures/fixture-generation-service.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { logger } from '@/utils/logger.js';
import { SQSBatchItemFailure } from 'aws-lambda';

interface FixtureGenerationRequest {
  gameworldId: string;
  leagueId: string;
}

/**
 * Process a single fixture generation request within a database transaction
 */
async function processFixtureGenerationRequest(
  request: FixtureGenerationRequest,
  transactionService: TransactionService,
  fixtureGenerationService: FixtureGenerationService,
  databaseService: FixtureGenerationDatabaseService
): Promise<void> {
  const { gameworldId, leagueId } = request;
  const correlationId = `fixture-queue-${gameworldId}-${leagueId}-${Date.now()}`;

  logger.debug('Processing fixture generation request from queue', {
    gameworldId,
    leagueId,
    correlationId,
  });

  // 2. Prepare all data needed for fixture generation (outside transaction)
  const fixtureData = await fixtureGenerationService.prepareFixtureGenerationData(
    gameworldId,
    leagueId
  );

  // 3. Validate the generated data
  fixtureGenerationService.validateFixtureData(fixtureData);

  // 4. Execute all database operations within a transaction
  await transactionService.executeInTransaction(async (em) => {
    try {
      await databaseService.executeFixtureGeneration(em, fixtureData, fixtureGenerationService);
    } catch (error) {
      logger.error('Failed to execute fixture generation in transaction', {
        gameworldId,
        leagueId,
        correlationId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  });

  const stats = fixtureGenerationService.getFixtureStatistics(fixtureData);

  logger.info('Fixture generation request processed successfully', {
    gameworldId,
    leagueId,
    correlationId,
    ...stats,
  });
}

/**
 * SQS handler for processing fixture generation requests
 */
const main = async function (event: SQSEvent<FixtureGenerationRequest>): Promise<{
  batchItemFailures: { itemIdentifier: string }[];
}> {
  const { repositories } = event.context;
  const batchItemFailures: SQSBatchItemFailure[] = [];

  // Initialize services
  const mikroOrmService = await getMikroOrmService();
  const transactionService = new TransactionService(mikroOrmService);
  const fixtureGenerationService = new FixtureGenerationService(repositories);
  const databaseService = new FixtureGenerationDatabaseService(repositories);

  logger.debug('Processing fixture generation queue batch', {
    recordCount: event.Records.length,
  });

  // Process each record in the batch
  for (const record of event.Records) {
    let request: FixtureGenerationRequest = record.body;
    try {
      const startTime = Date.now();

      logger.debug('Processing fixture generation record', {
        messageId: record.messageId,
        gameworldId: request.gameworldId,
        leagueId: request.leagueId,
      });

      // Validate required fields
      if (!request.gameworldId || !request.leagueId) {
        throw new Error('Missing required fields: gameworldId and leagueId');
      }

      // Process the fixture generation request
      await processFixtureGenerationRequest(
        request,
        transactionService,
        fixtureGenerationService,
        databaseService
      );

      // Record success metrics
      const processingTime = Date.now() - startTime;
      await databaseService.recordSuccessMetrics(
        { gameworldId: request.gameworldId, leagueId: request.leagueId } as any,
        processingTime
      );

      logger.debug('Fixture generation record processed successfully', {
        messageId: record.messageId,
        gameworldId: request.gameworldId,
        leagueId: request.leagueId,
        processingTime,
      });
    } catch (error) {
      logger.error('Failed to process fixture generation record', {
        error: error instanceof Error ? error.message : 'Unknown error',
        messageId: record.messageId,
        body: record.body,
        stack: error instanceof Error ? error.stack : undefined,
      });
      batchItemFailures.push({ itemIdentifier: record.messageId });
    }

    try {
      await databaseService.deleteCompletedFixtureDetails(request.gameworldId, request.leagueId);
    } catch (e) {
      logger.warn('Failed to delete completed fixture details', {
        error: e instanceof Error ? e.message : 'Unknown error',
        gameworldId: request.gameworldId,
        leagueId: request.leagueId,
        stack: e instanceof Error ? e.stack : undefined,
      });
    }
  }

  logger.debug('Fixture generation queue batch processing completed', {
    totalRecords: event.Records.length,
    successfulRecords: event.Records.length - batchItemFailures.length,
    failedRecords: batchItemFailures.length,
  });

  // Return batch item failures for SQS partial batch failure handling
  return {
    batchItemFailures,
  };
};

export const handler = sqsMiddify<FixtureGenerationRequest>(main, {});
