import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Team } from '@/entities/Team.js';
import { generateRandomTeamName } from '@/functions/generate/random-team-name.js';
import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';

import { STARTING_BALANCE } from '@/functions/generate/constants.js';
import { SQS } from '@/services/sqs/sqs.js';
import { LeagueRepository } from '@/storage-interface/leagues/league-repository.interface.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { LeagueTableCreatedEvent } from '@/types/generated/league-table-created-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { v4 as uuidv4 } from 'uuid';

const sqs = new SQS({ tracer });

function generateTeam(
  leagueRepository: LeagueRepository,
  gameworldId: string,
  leagueId: string,
  tier: number
): Team {
  const team = new Team();

  // Set required properties
  team.teamId = uuidv4();
  team.gameworldId = gameworldId;
  team.tier = tier;
  team.teamName = generateRandomTeamName();

  // Set default values
  team.balance = STARTING_BALANCE;
  team.played = 0;
  team.points = 0;
  team.goalsFor = 0;
  team.goalsAgainst = 0;
  team.wins = 0;
  team.draws = 0;
  team.losses = 0;
  team.selectionOrder = [];

  team.league = leagueRepository.createFromPK(leagueId);

  return team;
}

async function generateTeams(
  body: LeagueTableCreatedEvent,
  leagueRepository: LeagueRepository,
  teamRepository: TeamRepository
): Promise<{ teams: Team[] }> {
  const startTime = Date.now();
  logger.debug('Starting team generation', {
    requiredTeams: body.requiredTeams,
    leagueId: body.leagueId,
    gameworldId: body.gameworldId,
    startTime: startTime,
  });

  // Generate team objects
  const teamGenStart = Date.now();
  const teams: Team[] = [];
  for (let i = 0; i < body.requiredTeams; i++) {
    teams.push(generateTeam(leagueRepository, body.gameworldId, body.leagueId, body.tier));
  }
  logger.debug('Team objects generated', {
    count: teams.length,
    duration: Date.now() - teamGenStart,
  });

  if (teams.length === 0) {
    return { teams: [] };
  }

  try {
    // Insert teams
    const teamInsertStart = Date.now();
    logger.debug('Starting team insertion', { count: teams.length });
    await teamRepository.batchInsertTeams(teams);
    logger.debug('Teams insertion completed', {
      duration: Date.now() - teamInsertStart,
    });

    // Add teams to AvailableTeams if availableToManage is true
    if (body.availableToManage) {
      const availableTeamsStart = Date.now();
      logger.debug('Starting available teams insertion');
      const availableTeams: AvailableTeam[] = teams.map((team) => {
        const availableTeam = new AvailableTeam();
        availableTeam.id = uuidv4();
        availableTeam.gameworldId = team.gameworldId;
        availableTeam.teamId = team.teamId; // Direct ID assignment instead of reference
        return availableTeam;
      });
      await teamRepository.batchInsertAvailableTeams(availableTeams);
      logger.debug('Available teams insertion completed', {
        duration: Date.now() - availableTeamsStart,
      });
    }

    // Trigger fixtures generation via Lambda invoke
    const fixturesStart = Date.now();
    logger.debug('Triggering fixtures generation via Lambda invoke');

    const queueUrl = process.env.FIXTURE_GENERATION_QUEUE_URL;

    if (!queueUrl) {
      throw new Error('FIXTURE_GENERATION_QUEUE_URL environment variable not set');
    }

    // Send requests to SQS queue for processing
    await sqs.send(
      queueUrl,
      JSON.stringify({
        gameworldId: body.gameworldId,
        leagueId: body.leagueId,
      })
    );

    logger.debug('Fixtures generation triggered', {
      duration: Date.now() - fixturesStart,
    });

    logger.debug('Team generation process completed', {
      totalDuration: Date.now() - startTime,
    });

    return { teams };
  } catch (error) {
    logger.error('Team generation process failed', {
      error,
      duration: Date.now() - startTime,
    });
    throw error;
  }
}

/**
 * Generate a team in response to an SQS event
 * @param event
 */
async function main(event: SQSEvent<LeagueTableCreatedEvent>) {
  const { teamRepository, leagueRepository } = event.context.repositories;

  const mainStart = Date.now();
  logger.debug('Starting main handler', {
    recordCount: event.Records.length,
  });

  const batchItemFailures: any[] = [];
  const results = await Promise.allSettled(
    event.Records.map((record) => generateTeams(record.body, leagueRepository, teamRepository))
  );

  results.forEach((result, index) => {
    if (result.status === 'rejected') {
      logger.error('Team generation failed', {
        error: result.reason,
        body: event.Records[index]!.body,
      });
      batchItemFailures.push({ itemIdentifier: event.Records[index]!.messageId });
    }
  });

  const aggregatedResults = {
    teams: results.filter((r) => r.status === 'fulfilled').flatMap((r) => r.value.teams),
  };

  logger.debug('Teams created successfully', {
    duration: Date.now() - mainStart,
  });

  // SQS Publishing
  const sqsStart = Date.now();
  const sqsClient = new SQS({ tracer });
  // Process messages in batches for better logging
  const batchSize = 10;

  logger.debug('Starting SQS message publishing', {
    totalMessages: aggregatedResults.teams.length,
  });

  for (let i = 0; i < aggregatedResults.teams.length; i += batchSize) {
    const batchStart = Date.now();
    const batch = aggregatedResults.teams.slice(i, i + batchSize);
    const promises = batch.map((team) =>
      sqsClient.send(
        process.env.PLAYER_QUEUE_URL!,
        JSON.stringify({
          gameworldId: team.gameworldId,
          leagueId: team.league.id,
          tier: team.tier,
          teamId: team.teamId,
          requiredPlayers: 15,
        })
      )
    );

    await Promise.all(promises);
    logger.debug('SQS batch sent', {
      batchNumber: Math.floor(i / batchSize) + 1,
      batchSize: batch.length,
      duration: Date.now() - batchStart,
    });
  }

  logger.debug('Handler execution completed', {
    totalDuration: Date.now() - mainStart,
    sqsPublishingDuration: Date.now() - sqsStart,
  });

  return { batchItemFailures };
}

export const handler = sqsMiddify<LeagueTableCreatedEvent>(main, {});
