import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { IAPService } from '@/services/iap/IAPService.js';
import { buildResponse } from '@/utils/buildResponse.js';

interface QueryStringParameters {
  productId: string;
}

function main(event: HttpEvent<void, void, QueryStringParameters>) {
  const productConfig = IAPService.getProductConfig(event.queryStringParameters.productId);
  return Promise.resolve(buildResponse(200, JSON.stringify({ config: productConfig })));
}

export const handler = httpMiddify(main, {
  injectRepositories: false,
  schema: {
    $schema: 'http://json-schema.org/draft-07/schema#',
    type: 'object',
    properties: {
      queryStringParameters: {
        type: 'object',
        properties: {
          productId: {
            type: 'string',
          },
        },
        required: ['productId'],
      },
    },
    required: ['queryStringParameters'],
  },
});
