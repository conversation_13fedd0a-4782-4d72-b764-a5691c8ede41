import { WebhookEvent } from '@/functions/iap/types.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpHandler } from '@/middleware/rest/types.js';
import { IAPService } from '@/services/iap/IAPService.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';

const main: HttpHandler<WebhookEvent, void, void> = async function (event) {
  try {
    logger.info('Received IAP webhook', {
      eventType: event.body.event.type,
      userId: event.body.event.app_user_id,
      eventId: event.body.event.id,
    });

    // Validate webhook authenticity (you may want to add RevenueCat signature verification here)
    if (!event.body.event.app_user_id) {
      logger.error('Missing app_user_id in webhook event');
      return buildResponse(400, JSON.stringify({ error: 'Missing app_user_id' }));
    }

    // Process the webhook event
    const iapService = new IAPService(event.context.repositories);
    await iapService.processWebhookEvent(event.body);

    logger.info('IAP webhook processed successfully', {
      eventType: event.body.event.type,
      userId: event.body.event.app_user_id,
      eventId: event.body.event.id,
    });

    return buildResponse(200, JSON.stringify({ success: true }));
  } catch (error) {
    logger.error('Error processing IAP webhook', {
      error,
      eventType: event.body.event.type,
      userId: event.body.event.app_user_id,
      eventId: event.body.event.id,
    });

    // Return 200 to prevent RevenueCat from retrying (log the error for investigation)
    // You might want to return 500 for retryable errors depending on your needs
    return buildResponse(200, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, { injectRepositories: true });
