import { Manager } from '@/entities/Manager.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { TeamRepository, TransactionType } from '@/storage-interface/teams/index.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';
import { isToday, isYesterday } from 'date-fns';

enum RewardType {
  SCOUT_TOKEN = 'scoutToken',
  MONEY = 'money',
  MAGIC_SPONGE = 'magicSponge',
  RED_CARD_APPEAL = 'redCardAppeal',
  TRAINING_BOOST = 'trainingBoost',
}

const rewards = [
  // Day 1
  [
    {
      type: RewardType.SCOUT_TOKEN,
      value: 1,
    },
  ],
  // Day 2
  [
    {
      type: RewardType.MONEY,
      value: 10000,
    },
    {
      type: RewardType.SCOUT_TOKEN,
      value: 1,
    },
  ],
  // Day 3
  [
    {
      type: RewardType.MAGIC_SPONGE,
      value: 1,
    },
    {
      type: RewardType.SCOUT_TOKEN,
      value: 1,
    },
  ],
  // Day 4
  [
    {
      type: RewardType.RED_CARD_APPEAL,
      value: 1,
    },
    {
      type: RewardType.SCOUT_TOKEN,
      value: 1,
    },
  ],
  // Day 5
  [
    {
      type: RewardType.MAGIC_SPONGE,
      value: 1,
    },
    {
      type: RewardType.SCOUT_TOKEN,
      value: 1,
    },
  ],
  // Day 6
  [
    {
      type: RewardType.TRAINING_BOOST,
      value: 1,
    },
    {
      type: RewardType.SCOUT_TOKEN,
      value: 1,
    },
  ],
  // Day 7
  [
    {
      type: RewardType.MONEY,
      value: 100000,
    },
    {
      type: RewardType.SCOUT_TOKEN,
      value: 1,
    },
  ],
] as const;

const tierMultipiler = [1, 10, 5, 3, 1] as const;

async function rewardPlayerForDay(
  day: number,
  manager: Manager,
  teamRepository: TeamRepository,
  clonedRewards: typeof rewards
) {
  const reward = clonedRewards[day]!;
  for (const r of reward) {
    switch (r.type) {
      case RewardType.SCOUT_TOKEN:
        manager.scoutTokens = (manager.scoutTokens || 0) + r.value;
        break;
      case RewardType.MONEY:
        await teamRepository.updateTeamBalance(
          manager.team!.teamId,
          manager.gameworldId!,
          r.value,
          TransactionType.DAILY_REWARD
        );
        break;
      case RewardType.MAGIC_SPONGE:
        manager.magicSponges = (manager.magicSponges || 0) + r.value;
        break;
      case RewardType.RED_CARD_APPEAL:
        manager.cardAppeals = (manager.cardAppeals || 0) + r.value;
        break;
      case RewardType.TRAINING_BOOST:
        manager.trainingBoosts = (manager.trainingBoosts || 0) + r.value;
        break;
      default:
        throw new Error(`Unknown reward type: ${r['type']}`);
    }
  }
}

export const main = async function (event: HttpEvent<void, void, void>) {
  const { managerRepository, teamRepository } = event.context.repositories;
  const userId = getUser(event);
  const manager = await managerRepository.getManagerById(userId);
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  // multiply the money rewards by the tier multiplier
  const clonedRewards = rewards.map((day) => day.map((reward) => ({ ...reward })));
  clonedRewards.forEach((day) => {
    day.forEach((reward) => {
      if (reward.type === RewardType.MONEY) {
        const multiplier =
          tierMultipiler[Math.max(0, Math.min(tierMultipiler.length - 1, manager.team!.tier))]!;

        reward.value *= multiplier;
      }
    });
  });

  const lastLogin = Number(manager.lastActive);
  // if last login was yesterday, give them a reward
  if (lastLogin && isYesterday(new Date(lastLogin))) {
    const day = manager.loginStreak % clonedRewards.length;
    const em = managerRepository.getEntityManager();
    await rewardPlayerForDay(
      day,
      manager,
      teamRepository,
      clonedRewards as unknown as typeof rewards
    );
    em.assign(manager, {
      lastActive: new Date().getTime(),
      loginStreak: manager.loginStreak + 1,
      scoutTokens: manager.scoutTokens,
      magicSponges: manager.magicSponges,
      cardAppeals: manager.cardAppeals,
      trainingBoosts: manager.trainingBoosts,
    });
    await em.flush();
    logger.debug(`Rewarded player for day ${day}`);
    return buildResponse(
      200,
      JSON.stringify({ rewards: clonedRewards, loginStreak: manager.loginStreak, day })
    );
  } else if (lastLogin && isToday(new Date(lastLogin))) {
    // same day so we've already done all this
    logger.debug('Player has already been rewarded today');
    return buildResponse(202, JSON.stringify({}));
  } else {
    // longer than a daya go so reset the login streak
    const em = managerRepository.getEntityManager();
    await rewardPlayerForDay(
      0,
      manager,
      teamRepository,
      clonedRewards as unknown as typeof rewards
    );
    em.assign(manager, {
      lastActive: new Date().getTime(),
      loginStreak: 1,
      scoutTokens: manager.scoutTokens,
      magicSponges: manager.magicSponges,
      cardAppeals: manager.cardAppeals,
      trainingBoosts: manager.trainingBoosts,
    });
    await em.flush();
    logger.debug('Reset login streak and rewarded player for day 1');
    return buildResponse(
      200,
      JSON.stringify({ rewards: clonedRewards, loginStreak: manager.loginStreak, day: 0 })
    );
  }
};

export const handler = httpMiddify(main, {});
