import { Manager } from '@/entities/Manager.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

interface PathParameters {
  managerId?: string;
}
export type GetManagerEvent = HttpEvent<void, PathParameters, void>;

const main = async function (event: GetManagerEvent) {
  const { managerRepository } = event.context.repositories;

  let userId = getUser(event);
  if (event.pathParameters && event.pathParameters.managerId) {
    userId = event.pathParameters.managerId;
  }

  let manager: Manager | null = null;
  try {
    manager = await managerRepository.getManagerById(userId);
  } catch (error) {}
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  } else {
    return buildResponse(200, JSON.stringify(manager));
  }
};

export const handler = httpMiddify(main, {});
