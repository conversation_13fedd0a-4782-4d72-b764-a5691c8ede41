import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler } from '@/middleware/event/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { NewManagerEvent } from '@/types/generated/new-manager-event.js';
import { tracer } from '@/utils/tracer.js';
import { PostConfirmationTriggerEvent } from 'aws-lambda';

const sqsClient = new SQS({ tracer });

/**
 * This lambda is triggered by a cognito post confirmation event.
 * It will retry if the request doesn't complete in 5 seconds.
 * As connecting to the database takes 3 and the lambda cold start takes 2,
 * we put the request into a queue and return immediately.
 * @param event
 */
const main: EventHandler<PostConfirmationTriggerEvent> = async (event) => {
  const { userAttributes } = event.request;
  const { sub: userId } = userAttributes;

  const newManagerEvent: NewManagerEvent = {
    managerId: userId!,
    email: userAttributes.email,
  };
  await sqsClient.send(process.env.MANAGER_QUEUE_URL!, JSON.stringify(newManagerEvent));

  return event;
};

export const handler = eventMiddify(main, { injectRepositories: false });
