import { Manager } from '@/entities/Manager.ts';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { NotificationCategory } from '@/model/manager.js';
import { buildHtmlResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';

interface QueryStringParameters {
  id: string;
  category: string;
}

const main = async function (event: HttpEvent<void, void, QueryStringParameters>) {
  const { managerRepository } = event.context.repositories;

  // Check if the manager exists
  const manager = await managerRepository.getManagerById(event.queryStringParameters.id);
  if (!manager) {
    return buildHtmlResponse('Unsubscribe', 'Manager not found.');
  }

  // Turn off email notifications for the specified category
  const { category } = event.queryStringParameters;
  const updates: Partial<Manager> = {
    notificationPreferences: {
      ...manager.notificationPreferences,
      [category]: {
        ...((manager.notificationPreferences &&
          manager.notificationPreferences[category as NotificationCategory]) ||
          {}),
        email: false,
      },
    },
  };
  try {
    await managerRepository.updateManagerById(event.queryStringParameters.id, updates);
    return buildHtmlResponse('Unsubscribe', 'You have been successfully unsubscribed.');
  } catch (error) {
    logger.error('Error updating manager:', { error });
    return buildHtmlResponse('Unsubscribe', 'Failed to update manager.');
  }
};

export const handler = httpMiddify(main, { addHeaders: false });
