import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';

export type PingEvent = HttpEvent<void, void, void>;

const main = async function (_: PingEvent) {
  return Promise.resolve(buildResponse(200, JSON.stringify({ message: 'pong' })));
};

export const handler = httpMiddify(main, { injectRepositories: false });
