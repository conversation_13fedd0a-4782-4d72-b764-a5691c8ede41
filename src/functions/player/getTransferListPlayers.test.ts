import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { mockTransferRepository, resetAllRepositoryMocks } from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './getTransferListPlayers.js';

describe('Get Transfer List Players Handler', () => {
  const mockContext = {} as any;

  function createMockPlayer(override: Partial<TransferListedPlayer> = {}): TransferListedPlayer {
    const mockPlayer = PlayerFactory.build({});

    return {
      id: 'transfer-listed-player-id',
      player: mockPlayer,
      gameworldId: override.gameworldId || '',
      auctionStartPrice: 0,
      auctionCurrentPrice: 0,
      auctionListingCounter: 0,
      createdAt: 0,
      getHighestBid: vi.fn().mockReturnValue(null),
      isExpired: vi.fn().mockReturnValue(false),
      getWinningBid: vi.fn().mockReturnValue(null),
      auctionEndTime: 0,
      bidHistory: { getItems: () => [] } as any,
      ...override,
    };
  }

  beforeEach(() => {
    resetAllRepositoryMocks();
    process.env.TRANSFER_LISTED_PLAYERS_TABLE_NAME = 'transfer-listed-players-table';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should return players with default limit when no parameters provided', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue({
      players: [createMockPlayer(), createMockPlayer(), createMockPlayer()],
      lastEvaluatedKey: undefined,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toHaveLength(3);
    expect(body.lastEvaluatedKey).toBeUndefined();
    expect(mockTransferRepository.getTransferListedPlayers).toHaveBeenCalledWith(
      'test-gameworld',
      25,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined
    );
  });

  it('should respect custom limit parameter', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { limit: '2' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue({
      players: [createMockPlayer(), createMockPlayer()],
      lastEvaluatedKey: undefined,
    });

    const response = await handler(event, mockContext);

    expect(response.statusCode).toBe(200);
    expect(mockTransferRepository.getTransferListedPlayers).toHaveBeenCalledWith(
      'test-gameworld',
      2,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined
    );
  });

  it('should handle pagination with lastEvaluatedKey', async () => {
    const lastEvaluatedKeyBase64 = Buffer.from(
      JSON.stringify({
        offset: 25,
      })
    ).toString('base64');

    const nextPageKey = Buffer.from(
      JSON.stringify({
        offset: 50,
      })
    ).toString('base64');

    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { lastEvaluatedKey: lastEvaluatedKeyBase64 },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue({
      players: [createMockPlayer()],
      lastEvaluatedKey: nextPageKey,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toHaveLength(1);
    expect(body.lastEvaluatedKey).toBe(nextPageKey);
    expect(mockTransferRepository.getTransferListedPlayers).toHaveBeenCalledWith(
      'test-gameworld',
      25,
      lastEvaluatedKeyBase64,
      undefined,
      undefined,
      undefined,
      undefined
    );
  });

  it('should handle invalid lastEvaluatedKey', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { lastEvaluatedKey: 'invalid-base64' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue({
      players: [],
      lastEvaluatedKey: undefined,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toEqual([]);
    expect(body.lastEvaluatedKey).toBeUndefined();
  });

  it('should handle case when no players are found', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue({
      players: [],
      lastEvaluatedKey: undefined,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toEqual([]);
    expect(body.lastEvaluatedKey).toBeUndefined();
  });

  it('should handle case when query returns undefined', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
    });

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toEqual([]);
    expect(body.lastEvaluatedKey).toBeUndefined();
  });

  it('should return lastEvaluatedKey when there are more pages', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld' },
      queryStringParameters: { limit: '2' },
    });

    const nextPageKey = Buffer.from(
      JSON.stringify({
        offset: 2,
      })
    ).toString('base64');

    vi.mocked(mockTransferRepository.getTransferListedPlayers).mockResolvedValue({
      players: [createMockPlayer(), createMockPlayer()],
      lastEvaluatedKey: nextPageKey,
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.players).toHaveLength(2);
    expect(body.lastEvaluatedKey).toBe(nextPageKey);
  });
});
