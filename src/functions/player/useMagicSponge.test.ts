import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockPlayerRepository,
  resetAllRepositoryMocks,
} from '@/testing/mockRepositories.js';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './useMagicSponge.js';

describe('Use Magic Sponge Handler', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    resetAllRepositoryMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns 404 when manager or team is not found', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Manager or team not found');
  });

  it('returns 404 when player is not found', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue(null);

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(404);
    expect(body.error).toBe('Player not found');
  });

  it("returns 403 when player does not belong to the user's team", async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue({
      team: { teamId: 'other-team-id' },
    });

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(403);
    expect(body.error).toBe('Forbidden');
    expect(body.message).toBe('You can only use a magic sponge on players from your own team');
  });

  it('heals injured player by reducing injury duration', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    const injuredUntil = Date.now() + 1000 * 60 * 60 * 48; // 48 hours from now
    const player = { team: { teamId: 'team-id' }, injuredUntil };

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue(player);
    vi.mocked(mockPlayerRepository.updatePlayer).mockResolvedValue({});

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.injuredUntil).toBe(injuredUntil - 1000 * 60 * 60 * 24); // 24 hours reduced
    expect(mockPlayerRepository.updatePlayer).toHaveBeenCalledWith(player);
  });

  it('restores energy for non-injured player', async () => {
    const event = createHttpEvent({
      pathParameters: { gameworldId: 'test-gameworld', playerId: 'player-id' },
      headers: { Authorization: 'Bearer valid-token' },
    });

    const player = { team: { teamId: 'team-id' }, energy: 50 };

    vi.mocked(mockManagerRepository.getManagerById).mockResolvedValue({
      team: { teamId: 'team-id' },
    });
    vi.mocked(mockPlayerRepository.getPlayer).mockResolvedValue(player);
    vi.mocked(mockPlayerRepository.updatePlayer).mockResolvedValue({});

    const response = await handler(event, mockContext);
    const body = JSON.parse(response.body);

    expect(response.statusCode).toBe(200);
    expect(body.energy).toBe(100); // Energy restored to 100
    expect(mockPlayerRepository.updatePlayer).toHaveBeenCalledWith(player);
  });
});
