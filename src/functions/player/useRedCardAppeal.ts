import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

/**
 * Path parameters for useRedCardAppeal API endpoint
 */
interface RedCardAppealPathParameters {
  gameworldId: string;
  playerId: string;
}

/**
 * Lambda function to use a red card appeal on a player to reduce suspension
 * Player must belong to the user's team and have suspension games > 0
 */
const main = async function (event: HttpEvent<void, RedCardAppealPathParameters, void>) {
  const { playerRepository, managerRepository } = event.context.repositories;

  // Get the current user ID
  const userId = getUser(event);
  if (!userId) {
    return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
  }

  // Get path parameters
  const { gameworldId, playerId } = event.pathParameters;

  // Get the manager to find the team ID
  const manager = await managerRepository.getManagerById(userId);
  if (!manager || !manager.team) {
    return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
  }

  const teamId = manager.team.teamId;

  // Get the player
  const player = await playerRepository.getPlayer(gameworldId, playerId);
  if (!player) {
    return buildResponse(404, JSON.stringify({ error: 'Player not found' }));
  }

  // Check if the player belongs to the user's team
  if (player.team?.teamId !== teamId) {
    return buildResponse(
      403,
      JSON.stringify({
        error: 'Forbidden',
        message: 'You can only use a red card appeal on players from your own team',
      })
    );
  }

  // Check if player has suspension games to reduce
  if (player.suspendedForGames <= 0) {
    return buildResponse(
      400,
      JSON.stringify({
        error: 'Bad Request',
        message: 'Player is not suspended or has no suspension games remaining',
      })
    );
  }

  // Check if manager has card appeals available
  if (manager.cardAppeals <= 0) {
    return buildResponse(
      400,
      JSON.stringify({
        error: 'Bad Request',
        message: 'No card appeals available',
      })
    );
  }

  // Reduce suspension by 1 game
  player.suspendedForGames -= 1;

  // Update manager card appeal count and player suspension
  await managerRepository.updateCardAppealCount(manager.managerId, -1);
  await playerRepository.updatePlayer(player);

  return buildResponse(200, JSON.stringify(player));
};

export const handler = httpMiddify(main, {});
