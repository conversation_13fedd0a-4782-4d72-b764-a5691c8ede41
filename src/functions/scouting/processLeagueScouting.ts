import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { ScoutingRequest } from '@/model/scouting.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { logger } from '@/utils/logger.js';

const notificationManager = NotificationManager.getInstance();
async function main(event: SQSEvent<ScoutingRequest>) {
  // Get the scoutingRepository from the repositories
  const { scoutingRepository } = event.context.repositories;

  // Group requests by gameworld and league
  const requestsByGameworldAndLeague: { [key: string]: ScoutingRequest[] } = {};

  for (const record of event.Records) {
    const request = record.body;
    // Add validation to ensure leagueId exists
    if (!request.id) {
      logger.error('Invalid scouting request - missing leagueId', { request });
      continue;
    }
    const key = `${request.gameworldId}#${request.id}`;
    if (!requestsByGameworldAndLeague[key]) {
      requestsByGameworldAndLeague[key] = [];
    }
    requestsByGameworldAndLeague[key].push(request);
  }

  // Process each group of requests
  for (const [key, requests] of Object.entries(requestsByGameworldAndLeague)) {
    const [gameworldId, leagueId] = key.split('#');

    // Add validation for required IDs
    if (!gameworldId || !leagueId) {
      logger.error('Invalid key format - missing required IDs', { key });
      continue;
    }

    logger.debug('Processing league scouting requests', {
      gameworldId,
      leagueId,
      requestCount: requests.length,
    });

    // Process each request in the group
    for (const request of requests) {
      try {
        // Scout 5 random players from the league
        const scoutedPlayers = await scoutingRepository.scoutRandomPlayersFromLeague(
          gameworldId,
          leagueId,
          request.teamId,
          5
        );

        await notificationManager.loadManagerPreferences(
          request.managerId,
          event.context.repositories
        );
        await notificationManager.sendScoutingCompleteNotification();
      } catch (error) {
        logger.error('Error processing league scouting request', {
          error,
          gameworldId,
          leagueId,
          teamId: request.teamId,
        });
        // Let SQS retry the request
        throw error;
      }
    }
  }
}

export const handler = sqsMiddify<ScoutingRequest>(main, {});
