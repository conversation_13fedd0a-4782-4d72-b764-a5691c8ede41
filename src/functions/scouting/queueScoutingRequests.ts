import { eventMiddify } from '@/middleware/event/index.js';
import { EventHand<PERSON> } from '@/middleware/event/types.js';
import { ScoutingRequest as ScoutingRequestModel } from '@/model/scouting.js';
import { SNS } from '@/services/sns/index.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { ScheduledEvent } from 'aws-lambda';

const sns = new SNS({ tracer });

const main: EventHandler<ScheduledEvent, void> = async function (event): Promise<void> {
  if (!process.env.SCOUTING_TOPIC_ARN) {
    throw new Error('SCOUTING_TOPIC_ARN environment variable not set');
  }

  try {
    // Get the scouting request repository from the event context
    const { scoutingRequestRepository } = event.context.repositories;

    const currentTime = Date.now();
    logger.debug('Processing scouting requests', { currentTime });

    // Get pending scouting requests that are ready to be processed
    const pendingRequests = await scoutingRequestRepository.getPendingScoutingRequests(100);

    if (pendingRequests.length === 0) {
      logger.debug('No scouting requests ready for processing');
      return;
    }

    logger.debug('Found scouting requests to process', { pendingRequests });

    // Convert entity objects to the model format expected by SNS
    const requests: ScoutingRequestModel[] = pendingRequests.map((request) => ({
      type: request.type,
      id: request.targetId,
      pk: `${request.gameworldId}#${request.team.teamId}`,
      teamId: request.team.teamId,
      gameworldId: request.gameworldId,
      managerId: request.managerId,
      requestId: request.requestId,
      processAfter: request.processAfter,
    }));

    // Process requests in batches of 10 (SNS batch limit)
    const batchSize = 10;
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const entries = batch.map((request, index) => ({
        Id: `${i + index}`,
        Message: JSON.stringify(request),
        MessageAttributes: {
          DataType: { DataType: 'String', StringValue: request.type },
        },
      }));

      // Publish batch to SNS
      await sns.publishBatch(process.env.SCOUTING_TOPIC_ARN, entries);

      // Mark requests as processed
      await Promise.all(
        batch.map(async (request) => {
          await scoutingRequestRepository.markScoutingRequestAsProcessed(request.requestId);
        })
      );
    }

    logger.info('Successfully processed scouting requests', {
      processedCount: requests.length,
    });
  } catch (error) {
    logger.error('Error processing scouting requests', { error });
    throw error;
  }
};

export const handler = eventMiddify(main);
