import { ScoutingRequestType } from '@/entities/ScoutingRequest.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { TransactionType } from '@/storage-interface/teams/index.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface RequestScoutingBody {
  type: 'player' | 'team' | 'league';
  id: string;
}

type RequestScoutingEvent = HttpEvent<RequestScoutingBody, void, void>;

const SCOUTING_COST = 5000; // move to a config file?

const main = async function (event: RequestScoutingEvent) {
  const { type, id } = event.body;
  const userId = getUser(event);

  // Get repositories from the event context
  const { managerRepository, teamRepository, scoutingRequestRepository } =
    event.context.repositories;

  // Get the manager to verify scout tokens
  const manager = await managerRepository.getManagerById(userId);

  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  if (!manager.team) {
    return buildResponse(424, JSON.stringify({ error: 'Manager does not have a team assigned' }));
  }

  if (!manager.scoutTokens || manager.scoutTokens < 1) {
    return buildResponse(400, JSON.stringify({ error: 'No scouting tokens remaining' }));
  }

  // Get the team to verify balance
  const team = await teamRepository.getTeam(manager.gameworldId!, manager.team.teamId, false);

  if (!team) {
    return buildResponse(404, JSON.stringify({ error: 'Team not found' }));
  }

  if (team.balance < SCOUTING_COST) {
    return buildResponse(
      400,
      JSON.stringify({
        error: `Insufficient funds. Required: ${SCOUTING_COST}, Available: ${team.balance}`,
      })
    );
  }

  try {
    // Update manager's scout tokens
    const updatedScoutTokens = (manager.scoutTokens || 0) - 1;
    await managerRepository.updateManagerById(manager.managerId, {
      scoutTokens: updatedScoutTokens,
    });

    // Update team's balance
    await teamRepository.updateTeamBalance(
      team.teamId,
      team.gameworldId,
      -SCOUTING_COST,
      TransactionType.SCOUTING
    );

    // Create a new scouting request
    const processAfter = new Date().getTime() + 2 * 60 * 60 * 1000; // 2 hours from now
    await scoutingRequestRepository.createScoutingRequest(
      manager.gameworldId!,
      manager.team.teamId,
      manager.managerId,
      type as ScoutingRequestType, // Convert string type to enum
      id,
      processAfter
    );

    return buildResponse(
      200,
      JSON.stringify({
        message: 'Scouting request successful',
        remainingTokens: updatedScoutTokens,
        newBalance: team.balance - SCOUTING_COST,
      })
    );
  } catch (error) {
    logger.error('Error processing scouting request:', { error });
    throw error;
  }
};

export const handler = httpMiddify(main, {});
