import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { ScoutingRequest as ScoutingRequestModel } from '@/model/scouting.js';
import { SNS } from '@/services/sns/index.js';
import { TransactionType } from '@/storage-interface/teams/index.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';

interface RequestScoutingBody {
  type: 'player' | 'team' | 'league';
  id: string;
}

type RequestScoutingEvent = HttpEvent<RequestScoutingBody, void, void>;

const SCOUTING_COST = 5000; // move to a config file?
const sns = new SNS({ tracer });

/**
 * Immediately queue the scouting request instead of waiting and batching it
 * @param event
 */
const main = async function (event: RequestScoutingEvent) {
  if (!process.env.SCOUTING_TOPIC_ARN) {
    throw new Error('SCOUTING_TOPIC_ARN environment variable not set');
  }
  logger.debug('Requesting super scouting', { body: event.body });

  const { type, id } = event.body;
  const userId = getUser(event);

  // Get repositories from the event context
  const { managerRepository, teamRepository, scoutingRequestRepository } =
    event.context.repositories;

  // Get the manager to verify scout tokens
  const manager = await managerRepository.getManagerById(userId);

  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  if (!manager.team) {
    return buildResponse(424, JSON.stringify({ error: 'Manager does not have a team assigned' }));
  }

  if (!manager.superScoutTokens || manager.superScoutTokens < 1) {
    return buildResponse(400, JSON.stringify({ error: 'No super scouting tokens remaining' }));
  }

  // Get the team to verify balance
  const team = await teamRepository.getTeam(manager.gameworldId!, manager.team.teamId, false);

  if (!team) {
    return buildResponse(404, JSON.stringify({ error: 'Team not found' }));
  }

  if (team.balance < SCOUTING_COST) {
    return buildResponse(
      400,
      JSON.stringify({
        error: `Insufficient funds. Required: ${SCOUTING_COST}, Available: ${team.balance}`,
      })
    );
  }

  try {
    // Update manager's scout tokens
    const updatedScoutTokens = (manager.superScoutTokens || 0) - 1;
    await managerRepository.updateManagerById(manager.managerId, {
      superScoutTokens: updatedScoutTokens,
    });

    // Update team's balance
    await teamRepository.updateTeamBalance(
      team.teamId,
      team.gameworldId,
      -SCOUTING_COST,
      TransactionType.SCOUTING
    );

    const snsEvent: ScoutingRequestModel = {
      type,
      id,
      pk: `${manager.gameworldId}#${manager.team.teamId}`,
      teamId: manager.team.teamId,
      gameworldId: manager.gameworldId!,
      managerId: manager.managerId,
      requestId: 'request-1',
      processAfter: 0, // process immediately
    };
    await sns.publish(process.env.SCOUTING_TOPIC_ARN, {
      subject: 'Super scouting request',
      data: snsEvent,
    });

    return buildResponse(
      200,
      JSON.stringify({
        message: 'Scouting request successful',
        remainingTokens: updatedScoutTokens,
        newBalance: team.balance - SCOUTING_COST,
      })
    );
  } catch (error) {
    logger.error('Error processing scouting request:', { error });
    throw error;
  }
};

export const handler = httpMiddify(main, {});
