import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';
import { englishDataset, englishRecommendedTransformers, RegExpMatcher } from 'obscenity';

interface RequestBody {
  teamName: string;
}

const matcher = new RegExpMatcher({
  ...englishDataset.build(),
  ...englishRecommendedTransformers,
});

export type UpdateTeamNameEvent = HttpEvent<RequestBody, void, void>;

const main = async function (event: UpdateTeamNameEvent) {
  const { managerRepository, teamRepository } = event.context.repositories;

  // Get the current user ID from the event
  const userId = getUser(event);

  // Check if the manager exists and get their team
  const manager = await managerRepository.getManagerById(userId);
  if (!manager || !manager.team) {
    return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
  }

  const { teamName } = event.body;

  // Validate that team name is provided
  if (!teamName || teamName.trim() === '') {
    return buildResponse(400, JSON.stringify({ error: 'Team name must be provided' }));
  }

  // Validate team name length
  if (teamName.length > 100) {
    return buildResponse(
      400,
      JSON.stringify({ error: 'Team name must be 100 characters or less' })
    );
  }

  // Check for obscenity
  try {
    if (matcher.hasMatch(teamName)) {
      return buildResponse(406, JSON.stringify({ error: 'Oi! Children present!' }));
    }
  } catch (error) {
    logger.error('Error checking for obscenity:', { error });
    return buildResponse(500, JSON.stringify({ error: 'Failed to validate team name' }));
  }

  try {
    // Update the team name
    await teamRepository.updateTeamName(manager.team.teamId, manager.gameworldId!, teamName);

    // Set changedTeamName to true in manager
    await managerRepository.updateManagerById(userId, { changedTeamName: true });

    // Get the updated team to return in the response
    const updatedTeam = await teamRepository.getTeam(
      manager.gameworldId!,
      manager.team.teamId,
      false
    );

    return buildResponse(200, JSON.stringify(updatedTeam));
  } catch (error) {
    logger.error('Error updating team name:', { error });
    return buildResponse(500, JSON.stringify({ error: 'Failed to update team name' }));
  }
};

export const handler = httpMiddify(main, {});
