import createHttpEvent from '@/testing/createHttpEvent.js';
import {
  mockManagerRepository,
  mockPlayerRepository,
  mockTrainingRepository,
} from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { handler } from './assignTrainingSlot.js';

describe('assignTrainingSlot handler', () => {
  const mockContext = {} as any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockManagerRepository.getManagerById.mockResolvedValue({
      managerId: 'test-user-id',
      team: { teamId: 'test-team-id' },
      gameworldId: 'test-gameworld-id',
    });
    mockPlayerRepository.getPlayer.mockResolvedValue({
      playerId: 'player-1',
      attributes: {
        finishingCurrent: 50,
      },
    });
    mockTrainingRepository.getSlotsByTeam = vi.fn().mockResolvedValue([]);
  });

  it('assigns player and attribute to slot if slot exists', async () => {
    mockTrainingRepository.getSlotById.mockResolvedValue({ slotId: 'slot-1', slotIndex: 2 });
    mockTrainingRepository.assignPlayerToSlot.mockResolvedValue({});

    const event = createHttpEvent({
      body: { playerId: 'player-1', attribute: 'finishing' },
      pathParameters: {
        slotId: 'slot-1',
      },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ success: true });
    expect(mockTrainingRepository.assignPlayerToSlot).toHaveBeenCalledWith(
      'slot-1',
      'player-1',
      'finishing',
      50 // startValue
    );
    expect(mockTrainingRepository.createSlot).not.toHaveBeenCalled();
  });

  it('creates the slot if it does not exist', async () => {
    mockTrainingRepository.getSlotById.mockResolvedValue(undefined);
    mockTrainingRepository.createSlot.mockResolvedValue({});
    const event = createHttpEvent({
      body: { playerId: 'player-2', attribute: 'finishing', slotIndex: 0 },
      pathParameters: {
        slotId: 'new',
      },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ success: true });
    expect(mockTrainingRepository.createSlot).toHaveBeenCalledWith(
      expect.objectContaining({
        player: { playerId: 'player-2' },
        attribute: 'finishing',
        assignedAt: expect.any(Number),
        slotIndex: 0,
        team: { teamId: 'test-team-id' },
        startValue: 50,
      })
    );
    expect(mockTrainingRepository.assignPlayerToSlot).not.toHaveBeenCalled();
  });

  it('errors if it does not exist and its not slot 0', async () => {
    mockTrainingRepository.getSlotById.mockResolvedValue(undefined);
    mockTrainingRepository.createSlot.mockResolvedValue({});
    const event = createHttpEvent({
      body: { playerId: 'player-2', attribute: 'finishing', slotIndex: 2 },
      pathParameters: {
        slotId: 'new',
      },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Slot not found' });
    expect(mockTrainingRepository.createSlot).not.toHaveBeenCalled();
    expect(mockTrainingRepository.assignPlayerToSlot).not.toHaveBeenCalled();
  });

  it('fails validation if required params are missing', async () => {
    // Missing playerId
    const event = createHttpEvent({
      body: { attribute: 'finishing' },
      pathParameters: {
        slotId: 'slot-1',
      },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(422);
    const body = JSON.parse(response.body);

    expect(body.length).toBe(1);
    expect(body[0].message).toBe("must have required property 'playerId'");
  });

  it('returns 404 if the manager is not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue(null);
    const event = createHttpEvent({
      body: { playerId: 'player-1', attribute: 'finishing' },
      pathParameters: {
        slotId: 'slot-1',
      },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Manager or team not found' });
  });

  it('returns 404 if the player is not found', async () => {
    mockManagerRepository.getManagerById.mockResolvedValue({
      managerId: 'test-user-id',
      team: { teamId: 'test-team-id', gameworldId: 'test-gameworld-id' },
      gameworldId: 'test-gameworld-id',
    });
    mockPlayerRepository.getPlayer.mockResolvedValue(null);
    const event = createHttpEvent({
      body: { playerId: 'missing-player', attribute: 'finishing', slotIndex: 1 },
      pathParameters: { slotId: 'slot-1' },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(404);
    expect(JSON.parse(response.body)).toEqual({ error: 'Player not found' });
  });

  it('reuses an existing slot with the same index if slotId is new', async () => {
    const existingSlot = { slotId: 'slot-2', slotIndex: 3 };
    mockTrainingRepository.getSlotsByTeam = vi.fn().mockResolvedValue([existingSlot]);
    mockTrainingRepository.getSlotById.mockResolvedValue(undefined);
    mockTrainingRepository.assignPlayerToSlot.mockResolvedValue({});

    const event = createHttpEvent({
      body: { playerId: 'player-3', attribute: 'finishing', slotIndex: 3 },
      pathParameters: { slotId: 'new' },
      httpMethod: 'POST',
    });
    const response = await handler(event, mockContext);
    expect(response.statusCode).toBe(200);
    expect(JSON.parse(response.body)).toEqual({ success: true });
    expect(mockTrainingRepository.assignPlayerToSlot).toHaveBeenCalledWith(
      'new',
      'player-3',
      'finishing',
      50
    );
    expect(mockTrainingRepository.createSlot).not.toHaveBeenCalled();
  });
});
