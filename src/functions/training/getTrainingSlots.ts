import { Player } from '@/entities/Player.js';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { SLOT_UNLOCK_COSTS } from '@/functions/training/constants.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

interface TrainingSlotResponse {
  id?: string;
  slotId: number;
  player?: Partial<Player>;
  attribute?: string;
  startValue?: number;
  locked: boolean;
  unlockCost?: number;
}

const defaultSlotList: TrainingSlotResponse[] = [
  {
    slotId: 0,
    locked: false,
  },
  {
    slotId: 1,
    locked: true,
    unlockCost: SLOT_UNLOCK_COSTS[1],
  },
  {
    slotId: 2,
    locked: true,
    unlockCost: SLOT_UNLOCK_COSTS[2],
  },
  {
    slotId: 3,
    locked: true,
    unlockCost: SLOT_UNLOCK_COSTS[3],
  },
  {
    slotId: 4,
    locked: true,
    unlockCost: SLOT_UNLOCK_COSTS[4],
  },
];

const main = async (event: HttpEvent<void, void, void>) => {
  const { trainingRepository, managerRepository } = event.context.repositories;

  const userId = getUser(event);
  const manager = await managerRepository.getManagerById(userId);
  if (!manager || !manager.team) {
    return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
  }
  const teamId = manager.team.teamId;

  // Fetch slots for the team
  const slots: TeamTrainingSlot[] = await trainingRepository.getSlotsByTeam(teamId);

  // Map database slots by slotIndex for quick lookup
  const slotMap = new Map<number, TeamTrainingSlot>();
  slots.forEach((slot) => {
    slotMap.set(slot.slotIndex, slot);
    logger.debug('Fetched training slot', { attributes: slot.player?.attributes });
  });

  // Build the response array
  const responseSlots: TrainingSlotResponse[] = defaultSlotList.map((defaultSlot) => {
    const dbSlot = slotMap.get(defaultSlot.slotId);
    let currentValue: number | undefined;
    if (dbSlot && dbSlot.player && dbSlot.attribute) {
      currentValue = dbSlot.player.attributes[
        `${dbSlot.attribute}Current` as keyof PlayerAttributes
      ] as number;
    }

    if (dbSlot) {
      return {
        id: dbSlot.id,
        slotId: dbSlot.slotIndex,
        player: dbSlot.player
          ? {
              playerId: dbSlot.player.playerId,
              firstName: dbSlot.player.firstName,
              surname: dbSlot.player.surname,
            }
          : undefined,
        attribute: dbSlot.attribute ?? undefined,
        startValue: dbSlot.startValue ?? undefined,
        currentValue,
        locked: false,
      } as TrainingSlotResponse;
    }
    return defaultSlot;
  });

  return buildResponse(200, JSON.stringify({ slots: responseSlots }));
};

export const handler = httpMiddify(main, {});
