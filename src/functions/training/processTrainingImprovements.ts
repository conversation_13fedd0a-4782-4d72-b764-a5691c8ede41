import { sqsMiddify } from '@/middleware/sqs/index.js';
import { SQSEvent } from '@/middleware/sqs/types.js';
import { CurrentAttributes } from '@/model/player.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { AttributeUpdate } from '@/storage-interface/players/index.js';
import { TrainingImprovementEvent } from '@/types/generated/training-improvement-event.ts';

const notificationManager = NotificationManager.getInstance();

async function main(event: SQSEvent<TrainingImprovementEvent>) {
  const { playerRepository } = event.context.repositories;

  const attributes: AttributeUpdate[] = [];
  const batchItemFailures: { itemIdentifier: string }[] = [];

  for (const record of event.Records) {
    try {
      const isStamina = record.body.attribute === 'stamina';
      const progress_percentage = record.body.current / record.body.potential;
      const scaling_factor = 1 - 0.9 * progress_percentage;
      const attributeIncrement = Math.min(
        scaling_factor * record.body.trainingMultiplier,
        record.body.potential - record.body.current
      );

      // send notifications
      if (attributeIncrement < 0.01 && record.body.managerId) {
        await notificationManager.loadManagerPreferences(
          record.body.managerId,
          event.context.repositories
        );
        await notificationManager.sendTrainingCompleteNotification(
          record.body.playerName,
          record.body.attribute
        );
        continue;
      }

      attributes.push({
        playerId: record.body.playerId,
        attribute: isStamina
          ? 'stamina'
          : (`${record.body.attribute}Current` as keyof CurrentAttributes),
        attributeIncrement: attributeIncrement,
      });
    } catch (err) {
      // Add to batch item failures for partial batch response
      if (record.messageId) {
        batchItemFailures.push({ itemIdentifier: record.messageId });
      }
    }
  }

  if (attributes.length > 0) {
    try {
      await playerRepository.updatePlayerAttributesBatch(attributes);
    } catch (err) {
      // If batch update fails, mark all as failed
      for (const record of event.Records) {
        if (record.messageId) {
          batchItemFailures.push({ itemIdentifier: record.messageId });
        }
      }
    }
  }

  // Return batch item failures for SQS partial batch response
  return { batchItemFailures };
}

export const handler = sqsMiddify<TrainingImprovementEvent>(main, {});
