import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { TRAINING_MULTIPLIERS } from '@/functions/training/constants.js';
import { eventMiddify } from '@/middleware/event/index.ts';
import { EventHandler, EventWithRepositories } from '@/middleware/event/types.js';
import { SQS } from '@/services/sqs/sqs.js';
import { TrainingImprovementEvent } from '@/types/generated/training-improvement-event.js';
import { tracer } from '@/utils/tracer.js';
import { SendMessageBatchRequestEntry } from '@aws-sdk/client-sqs/dist-types/models/models_0.js';

const sqs = new SQS({ tracer });

const main: EventHandler<EventWithRepositories, void> = async (event) => {
  const { trainingRepository } = event.context.repositories;
  const filledSlots = await trainingRepository.getAllFilledSlots();

  if (filledSlots.length === 0) {
    return;
  }

  // Filter out slots that are already at potential
  const payload: SendMessageBatchRequestEntry[] = filledSlots
    .filter((slot) => {
      if (slot.attribute === 'stamina') {
        // For stamina, only allow improvement if not at max (1)
        return slot.player!.attributes['stamina'] < 1;
      }
      return (
        slot.player!.attributes[`${slot.attribute}Current` as keyof PlayerAttributes]! !==
        slot.player!.attributes[`${slot.attribute}Potential` as keyof PlayerAttributes]!
      );
    })
    .map((slot) => {
      let current, potential;
      if (slot.attribute === 'stamina') {
        current = slot.player!.attributes['stamina'];
        potential = 1; // Stamina potential is always 1
      } else {
        current = slot.player!.attributes[`${slot.attribute}Current` as keyof PlayerAttributes]!;
        potential =
          slot.player!.attributes[`${slot.attribute}Potential` as keyof PlayerAttributes]!;
      }
      const entry: SendMessageBatchRequestEntry = {
        Id: slot.id,
        MessageBody: JSON.stringify({
          playerId: slot.player!.playerId,
          playerName: `${slot.player!.firstName} ${slot.player!.surname}`,
          managerId: slot.team.manager?.managerId,
          attribute: slot.attribute,
          current,
          potential,
          trainingMultiplier:
            TRAINING_MULTIPLIERS[
              Math.max(0, Math.min(TRAINING_MULTIPLIERS.length, slot.team.trainingLevel - 1))
            ],
        } as TrainingImprovementEvent),
      };
      return entry;
    });

  if (payload.length === 0) {
    return;
  }
  await sqs.sendBatch(process.env.TRAINING_QUEUE_URL!, payload);
};

export const handler = eventMiddify(main, { injectRepositories: true });
