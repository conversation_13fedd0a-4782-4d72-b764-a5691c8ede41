import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

/**
 * Lambda function to get active transfer requests where the current user's team is either buyer or seller
 */
const main = async function (event: HttpEvent<void, void, void>) {
  try {
    const { transferRepository, managerRepository } = event.context.repositories;

    // Get the current user ID
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Get the manager to find the team ID
    const manager = await managerRepository.getManagerById(userId);
    if (!manager || !manager.team) {
      return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
    }

    const teamId = manager.team.teamId;

    // Get transfer requests where my team is the buyer
    const buyerRequests = await transferRepository.getTransferRequestsByBuyer(teamId);

    // Get transfer requests where my team is the seller
    const sellerRequests = await transferRepository.getTransferRequestsBySeller(teamId);

    // Combine both arrays
    const allActiveTransfers = [...buyerRequests, ...sellerRequests];

    logger.debug('Retrieved active transfers', {
      userId,
      teamId,
      buyerRequestsCount: buyerRequests.length,
      sellerRequestsCount: sellerRequests.length,
      totalCount: allActiveTransfers.length,
    });

    return buildResponse(200, JSON.stringify(allActiveTransfers));
  } catch (error) {
    logger.error('Failed to get active transfers:', { error });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {});
