import { httpMiddify } from '@/middleware/rest/index.ts';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';

interface Body {
  playerId: string;
  gameworldId: string;
  isTransferListed: boolean;
}

async function main(event: HttpEvent<Body, void, void>) {
  const { playerId, gameworldId, isTransferListed } = event.body;
  const { transferRepository, playerRepository, managerRepository } = event.context.repositories;

  const userId = getUser(event);

  // Get the manager to verify scout tokens
  const manager = await managerRepository.getManagerById(userId);
  if (!manager) {
    return buildResponse(404, JSON.stringify({ error: 'Manager not found' }));
  }

  const player = await playerRepository.getPlayer(gameworldId, playerId);
  if (!player) {
    return buildResponse(404, JSON.stringify({ error: 'Player not found' }));
  }

  if (player.team?.teamId !== manager.team?.teamId && manager.role !== 'admin') {
    return buildResponse(
      403,
      JSON.stringify({ error: 'You cannot change the status of players in another team' })
    );
  }

  if (!isTransferListed) {
    await transferRepository.deleteTransferListedPlayer(undefined, playerId);
  } else {
    if (player.isTransferListed) {
      return buildResponse(200, JSON.stringify({ message: 'Player already on transfer list' }));
    }
    await transferRepository.addTransferListedPlayer(player);
  }
  return buildResponse(200, JSON.stringify({ message: 'Player transfer status updated' }));
}

export const handler = httpMiddify(main, {
  schema: {
    type: 'object',
    properties: {
      body: {
        type: 'object',
        properties: {
          playerId: {
            type: 'string',
          },
          gameworldId: {
            type: 'string',
          },
          isTransferListed: {
            type: 'boolean',
          },
        },
        required: ['playerId', 'gameworldId', 'isTransferListed'],
        additionalProperties: false,
      },
    },
    required: ['body'],
  },
});
