import { Repositories } from '@/middleware/database/types.js';
import { EventWithRepositories } from '@/middleware/event/types.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { logger } from '@/utils/logger.js';
import { isColdStartExecution, markWarmStart, recordMetric, timeExecution } from '@/utils/performance.js';
import { Request } from '@middy/core';
import { performance } from 'perf_hooks';

// Repository cache for reuse across invocations
const repositoryCache = new Map<string, any>();
let isWarmStart = false;

/**
 * Optimized database middleware that reduces cold start time through:
 * 1. Lazy loading of repositories
 * 2. Repository caching
 * 3. Connection reuse
 * 4. Minimal initialization
 */
export const optimizedDatabaseMiddleware = () => {
  return {
    before: async (request: Request<EventWithRepositories>): Promise<void> => {
      const startTime = performance.now();
      const coldStart = isColdStartExecution();

      if (isWarmStart && !coldStart) {
        logger.debug('Warm start detected, reusing existing connections');
        // For warm starts, just inject cached repositories if available
        if (repositoryCache.size > 0) {
          request.event.context = request.event.context || {};
          request.event.context.repositories = createRepositoriesFromCache();
          const duration = performance.now() - startTime;
          recordMetric('totalDuration', duration);
          logger.debug(`Warm start repository injection: ${duration}ms`);
          return;
        }
      }

      logger.debug('Cold start - initializing database connection');

      try {
        // Initialize database connection (this is cached globally)
        const dbInitTime = await timeExecution('Database connection initialization', async () => {
          await getMikroOrmService();
        });
        recordMetric('dbInitDuration', dbInitTime);

        // Create repositories lazily - only create what we need
        const repositories = createLazyRepositories();

        // Ensure context exists
        if (!request.event.context) {
          request.event.context = {};
        }

        request.event.context.repositories = repositories;

        isWarmStart = true;
        markWarmStart();

        const totalTime = performance.now() - startTime;
        recordMetric('totalDuration', totalTime);
        logger.info(`Cold start database initialization completed in ${totalTime}ms`);

      } catch (error) {
        logger.error('Failed to initialize database connection', { error });
        throw new Error(
          `Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    },
    
    after: async (): Promise<void> => {
      // Don't close connections in Lambda - let them be reused
      // The connection will be closed when the Lambda container is destroyed
      logger.debug('Request completed, keeping connection alive for reuse');
    },
  };
};

/**
 * Creates repositories from cache for warm starts
 */
function createRepositoriesFromCache(): Repositories {
  return {
    leagueRepository: repositoryCache.get('leagueRepository'),
    teamRepository: repositoryCache.get('teamRepository'),
    fixtureRepository: repositoryCache.get('fixtureRepository'),
    playerRepository: repositoryCache.get('playerRepository'),
    managerRepository: repositoryCache.get('managerRepository'),
    purchaseRepository: repositoryCache.get('purchaseRepository'),
    scoutingRepository: repositoryCache.get('scoutingRepository'),
    scoutingRequestRepository: repositoryCache.get('scoutingRequestRepository'),
    gameworldRepository: repositoryCache.get('gameworldRepository'),
    transferRepository: repositoryCache.get('transferRepository'),
    inboxRepository: repositoryCache.get('inboxRepository'),
    trainingRepository: repositoryCache.get('trainingRepository'),
  };
}

/**
 * Creates lazy-loaded repositories that are only instantiated when accessed
 */
function createLazyRepositories(): Repositories {
  return {
    get leagueRepository() {
      return getOrCreateRepository('leagueRepository', async () => {
        const { MikroOrmLeagueRepository } = await import('@/storage-interface/leagues/mikro-orm-league-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmLeagueRepository(mikroOrmService);
      });
    },
    
    get teamRepository() {
      return getOrCreateRepository('teamRepository', async () => {
        const { MikroOrmTeamRepository } = await import('@/storage-interface/teams/mikro-orm-team-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmTeamRepository(mikroOrmService);
      });
    },
    
    get fixtureRepository() {
      return getOrCreateRepository('fixtureRepository', async () => {
        const { FixtureRepository } = await import('@/storage-interface/fixtures/fixture-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new FixtureRepository(mikroOrmService);
      });
    },
    
    get playerRepository() {
      return getOrCreateRepository('playerRepository', async () => {
        const { MikroOrmPlayerRepository } = await import('@/storage-interface/players/mikro-orm-player-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmPlayerRepository(mikroOrmService);
      });
    },
    
    get managerRepository() {
      return getOrCreateRepository('managerRepository', async () => {
        const { MikroOrmManagerRepository } = await import('@/storage-interface/managers/mikro-orm-manager-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmManagerRepository(mikroOrmService);
      });
    },
    
    get purchaseRepository() {
      return getOrCreateRepository('purchaseRepository', async () => {
        const { MikroOrmPurchaseRepository } = await import('@/storage-interface/purchases/mikro-orm-purchase-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmPurchaseRepository(mikroOrmService);
      });
    },
    
    get scoutingRepository() {
      return getOrCreateRepository('scoutingRepository', async () => {
        const { MikroOrmScoutingRepository } = await import('@/storage-interface/scouting/mikro-orm-scouting-repository.js');
        const playerRepository = await this.playerRepository;
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmScoutingRepository(mikroOrmService, playerRepository);
      });
    },
    
    get scoutingRequestRepository() {
      return getOrCreateRepository('scoutingRequestRepository', async () => {
        const { MikroOrmScoutingRequestRepository } = await import('@/storage-interface/scouting-requests/mikro-orm-scouting-request-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmScoutingRequestRepository(mikroOrmService);
      });
    },
    
    get gameworldRepository() {
      return getOrCreateRepository('gameworldRepository', async () => {
        const { MikroOrmGameworldRepository } = await import('@/storage-interface/gameworld/mikro-orm-gameworld-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmGameworldRepository(mikroOrmService);
      });
    },
    
    get transferRepository() {
      return getOrCreateRepository('transferRepository', async () => {
        const { MikroOrmTransferRepository } = await import('@/storage-interface/transfers/mikro-orm-transfer-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmTransferRepository(mikroOrmService);
      });
    },
    
    get inboxRepository() {
      return getOrCreateRepository('inboxRepository', async () => {
        const { DynamoInboxRepository } = await import('@/storage-interface/inbox/dynamo-inbox-repository.js');
        const { default: DynamoDbService } = await import('@/services/database/dynamo/dynamo-db-service.js');
        const dynamoDb = new DynamoDbService();
        return new DynamoInboxRepository(dynamoDb);
      });
    },
    
    get trainingRepository() {
      return getOrCreateRepository('trainingRepository', async () => {
        const { MikroOrmTeamTrainingSlotRepository } = await import('@/storage-interface/training/mikro-orm-training-repository.js');
        const mikroOrmService = await getMikroOrmService();
        return new MikroOrmTeamTrainingSlotRepository(mikroOrmService);
      });
    },
  };
}

/**
 * Gets or creates a repository with caching
 */
function getOrCreateRepository<T>(key: string, factory: () => Promise<T>): Promise<T> {
  if (repositoryCache.has(key)) {
    return Promise.resolve(repositoryCache.get(key));
  }
  
  const repositoryPromise = factory().then(repository => {
    repositoryCache.set(key, repository);
    return repository;
  });
  
  // Cache the promise to avoid duplicate creation
  repositoryCache.set(key, repositoryPromise);
  return repositoryPromise;
}
