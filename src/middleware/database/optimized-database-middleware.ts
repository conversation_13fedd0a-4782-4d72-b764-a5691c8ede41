import { Repositories } from '@/middleware/database/types.js';
import { EventWithRepositories } from '@/middleware/event/types.js';
import { getMikroOrmService } from '@/storage-interface/database-initializer.js';
import { logger } from '@/utils/logger.js';
import { isColdStartExecution, markWarmStart, recordMetric, timeExecution } from '@/utils/performance.js';
import { Request } from '@middy/core';
import { performance } from 'perf_hooks';

// Repository cache for reuse across invocations
const repositoryCache = new Map<string, any>();
let isWarmStart = false;

/**
 * Optimized database middleware that reduces cold start time through:
 * 1. Lazy loading of repositories
 * 2. Repository caching
 * 3. Connection reuse
 * 4. Minimal initialization
 */
export const optimizedDatabaseMiddleware = () => {
  return {
    before: async (request: Request<EventWithRepositories>): Promise<void> => {
      const startTime = performance.now();
      const coldStart = isColdStartExecution();

      if (isWarmStart && !coldStart) {
        logger.debug('Warm start detected, reusing existing connections');
        // For warm starts, just inject cached repositories if available
        if (repositoryCache.size > 0) {
          request.event.context = request.event.context || {};
          request.event.context.repositories = await createRepositoriesFromCache();
          const duration = performance.now() - startTime;
          recordMetric('totalDuration', duration);
          logger.debug(`Warm start repository injection: ${duration}ms`);
          return;
        }
      }

      logger.debug('Cold start - initializing database connection');

      try {
        // Initialize database connection and create repositories
        const repositories = await timeExecution('Database and repository initialization', async () => {
          return await createRepositories();
        });

        // Ensure context exists
        if (!request.event.context) {
          request.event.context = {};
        }

        request.event.context.repositories = repositories;

        isWarmStart = true;
        markWarmStart();

        const totalTime = performance.now() - startTime;
        recordMetric('totalDuration', totalTime);
        logger.info(`Cold start database initialization completed in ${totalTime}ms`);

      } catch (error) {
        logger.error('Failed to initialize database connection', { error });
        throw new Error(
          `Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    },
    
    after: async (): Promise<void> => {
      // Don't close connections in Lambda - let them be reused
      // The connection will be closed when the Lambda container is destroyed
      logger.debug('Request completed, keeping connection alive for reuse');
    },
  };
};

/**
 * Creates repositories from cache for warm starts
 */
async function createRepositoriesFromCache(): Promise<Repositories> {
  // If we have cached repositories, reuse them
  if (repositoryCache.size > 0) {
    return {
      leagueRepository: repositoryCache.get('leagueRepository'),
      teamRepository: repositoryCache.get('teamRepository'),
      fixtureRepository: repositoryCache.get('fixtureRepository'),
      playerRepository: repositoryCache.get('playerRepository'),
      managerRepository: repositoryCache.get('managerRepository'),
      purchaseRepository: repositoryCache.get('purchaseRepository'),
      scoutingRepository: repositoryCache.get('scoutingRepository'),
      scoutingRequestRepository: repositoryCache.get('scoutingRequestRepository'),
      gameworldRepository: repositoryCache.get('gameworldRepository'),
      transferRepository: repositoryCache.get('transferRepository'),
      inboxRepository: repositoryCache.get('inboxRepository'),
      trainingRepository: repositoryCache.get('trainingRepository'),
    };
  }

  // Otherwise create new repositories
  return await createRepositories();
}

/**
 * Creates repositories that are initialized immediately but cached for reuse
 */
async function createRepositories(): Promise<Repositories> {
  const mikroOrmService = await getMikroOrmService();

  // Import all repository classes
  const [
    { MikroOrmLeagueRepository },
    { MikroOrmTeamRepository },
    { FixtureRepository },
    { MikroOrmPlayerRepository },
    { MikroOrmManagerRepository },
    { MikroOrmPurchaseRepository },
    { MikroOrmScoutingRepository },
    { MikroOrmScoutingRequestRepository },
    { MikroOrmGameworldRepository },
    { MikroOrmTransferRepository },
    { DynamoInboxRepository },
    { MikroOrmTeamTrainingSlotRepository },
    { default: DynamoDbService }
  ] = await Promise.all([
    import('@/storage-interface/leagues/mikro-orm-league-repository.js'),
    import('@/storage-interface/teams/mikro-orm-team-repository.js'),
    import('@/storage-interface/fixtures/fixture-repository.js'),
    import('@/storage-interface/players/mikro-orm-player-repository.js'),
    import('@/storage-interface/managers/mikro-orm-manager-repository.js'),
    import('@/storage-interface/purchases/mikro-orm-purchase-repository.js'),
    import('@/storage-interface/scouting/mikro-orm-scouting-repository.js'),
    import('@/storage-interface/scouting-requests/mikro-orm-scouting-request-repository.js'),
    import('@/storage-interface/gameworld/mikro-orm-gameworld-repository.js'),
    import('@/storage-interface/transfers/mikro-orm-transfer-repository.js'),
    import('@/storage-interface/inbox/dynamo-inbox-repository.js'),
    import('@/storage-interface/training/mikro-orm-training-repository.js'),
    import('@/services/database/dynamo/dynamo-db-service.js')
  ]);

  // Create repositories
  const playerRepository = new MikroOrmPlayerRepository(mikroOrmService);
  const dynamoDb = new DynamoDbService();

  return {
    leagueRepository: getOrCreateCachedRepository('leagueRepository', () =>
      new MikroOrmLeagueRepository(mikroOrmService)
    ),
    teamRepository: getOrCreateCachedRepository('teamRepository', () =>
      new MikroOrmTeamRepository(mikroOrmService)
    ),
    fixtureRepository: getOrCreateCachedRepository('fixtureRepository', () =>
      new FixtureRepository(mikroOrmService)
    ),
    playerRepository: getOrCreateCachedRepository('playerRepository', () =>
      playerRepository
    ),
    managerRepository: getOrCreateCachedRepository('managerRepository', () =>
      new MikroOrmManagerRepository(mikroOrmService)
    ),
    purchaseRepository: getOrCreateCachedRepository('purchaseRepository', () =>
      new MikroOrmPurchaseRepository(mikroOrmService)
    ),
    scoutingRepository: getOrCreateCachedRepository('scoutingRepository', () =>
      new MikroOrmScoutingRepository(mikroOrmService, playerRepository)
    ),
    scoutingRequestRepository: getOrCreateCachedRepository('scoutingRequestRepository', () =>
      new MikroOrmScoutingRequestRepository(mikroOrmService)
    ),
    gameworldRepository: getOrCreateCachedRepository('gameworldRepository', () =>
      new MikroOrmGameworldRepository(mikroOrmService)
    ),
    transferRepository: getOrCreateCachedRepository('transferRepository', () =>
      new MikroOrmTransferRepository(mikroOrmService)
    ),
    inboxRepository: getOrCreateCachedRepository('inboxRepository', () =>
      new DynamoInboxRepository(dynamoDb)
    ),
    trainingRepository: getOrCreateCachedRepository('trainingRepository', () =>
      new MikroOrmTeamTrainingSlotRepository(mikroOrmService)
    ),
  };
}

/**
 * Gets or creates a repository with caching (synchronous version)
 */
function getOrCreateCachedRepository<T>(key: string, factory: () => T): T {
  if (repositoryCache.has(key)) {
    return repositoryCache.get(key);
  }

  const repository = factory();
  repositoryCache.set(key, repository);
  return repository;
}
