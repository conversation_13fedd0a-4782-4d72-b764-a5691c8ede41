import { defaultEventMiddlewares } from '@/middleware/event/middlewares.js';
import { EventHandler } from '@/middleware/event/types.js';
import middy from '@middy/core';

/**
 * @param handler - Event Lambda handler
 * @param options - Not used currently
 * @returns
 */
export const eventMiddify = <TEvent, TResult>(
  handler: EventHandler<TEvent, TResult>,
  options?: { injectRepositories: boolean }
) => {
  const wrapped = middy(handler);

  wrapped.use(defaultEventMiddlewares(options || { injectRepositories: true }));

  return wrapped;
};
