import { databaseMiddleware } from '@/middleware/database/index.js';
import { EventMiddlewareObj } from '@/middleware/event/types.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { injectLambdaContext } from '@aws-lambda-powertools/logger/middleware';
import { captureLambdaHandler } from '@aws-lambda-powertools/tracer/middleware';
import eventNormalizer from '@middy/event-normalizer';

export const defaultEventMiddlewares = function (options: { injectRepositories: boolean }) {
  const middlewares = [
    eventNormalizer(),
    injectLambdaContext(logger, {
      logEvent: true,
    }),
    captureLambdaHandler(tracer, {
      captureResponse: true,
    }),
  ] as EventMiddlewareObj[];
  if (options.injectRepositories) {
    middlewares.push(databaseMiddleware());
  }
  return middlewares;
};
