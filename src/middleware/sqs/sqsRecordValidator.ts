import middy from '@middy/core';
import { transpileSchema } from '@middy/validator/transpile';
import { SQSBatchResponse } from 'aws-lambda';

import { SQSEvent, SQSMiddlewareObj } from './types.js';

/**
 * Custom validator that validates the individual records in the queue rather than the whole queue object
 * @param schema the json schema for the event items
 */
export const sqsRecordValidator = <T>(schema: object): SQSMiddlewareObj<T> => {
  const validate = transpileSchema(schema);

  const before: middy.MiddlewareFn<SQSEvent<T>, SQSBatchResponse | void> = async (
    request
  ): Promise<void> => {
    // records have already been JSON parsed
    request.event.Records.forEach((record) => {
      const body = record?.body;

      // validate message
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore the typings are wrong. validate is a compiled ajv function
      const valid = validate(body);
      if (!valid) {
        // Bad Request - keep error responses consistent with the middy validator
        throw new Error('Event object failed validation', {
          cause: {
            package: 'sqsRecordValidator',
            data: validate.errors,
          },
        });
      }
    });
  };

  return {
    before,
  };
};
