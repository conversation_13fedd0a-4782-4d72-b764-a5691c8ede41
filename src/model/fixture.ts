export interface MatchPlayer {
  playerId: string;
  playerName: string;
  joinedMatchMinute?: number;
  leftMatchMinute?: number;
  rating?: number;
  sentOff?: number;
  injured?: number;
  goals?: number[];
  assists?: number[];
  substitute?: string;
}

export interface FixtureDetails {
  fixtureId: string;
  gameworldId: string;
  leagueId: string;
  homeTeamId: string;
  homeTeamName: string;
  awayTeamId: string;
  awayTeamName: string;
  date: number;
  stats: MatchStats;
  events: MatchEvent[];
  homePlayers: MatchPlayer[];
  awayPlayers: MatchPlayer[];
}

export interface Scorer {
  playerId: string;
  playerName: string;
  team: number;
  goalTime: {
    minute: number;
    half: number;
  }[];
}

export interface MatchStats {
  possession: [number, number];
  shots: [number, number];
  shotsOnTarget: [number, number];
  corners: [number, number];
  fouls: [number, number];
  yellowCards: [number, number];
  redCards: [number, number];
  passes: [number, number];
  passAccuracy: [number, number];
  tackles: [number, number];
  interceptions: [number, number];
  score: [number, number];
  scorers?: Scorer[];
}

// string ids that may be in this localisation
export interface PossibleEventSubstitution {
  team?: string;
  homeTeam?: string;
  oppTeam?: string;
  awayTeam?: string;
  player?: string;
  oppPlayer?: string;
  nextPlayer?: string;
  homeScore?: string;
  awayScore?: string;
}

export interface MatchEvent {
  localisationId: string;
  substitutions: PossibleEventSubstitution;
  minute: number;
  half: number;
}
