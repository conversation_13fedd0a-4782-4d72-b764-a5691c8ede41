import { FixtureGenerationService } from '@/services/fixtures/fixture-generation-service.js';
import { FixtureFactory } from '@/testing/factories/fixtureFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { createMockRepositories, mockTeamRepository } from '@/testing/mockRepositories.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the generateFixtures function
vi.mock('@/functions/generate/fixtures.js', () => ({
  generateFixtures: vi.fn().mockImplementation((teamRepo, leagueRepo, teams) => {
    return teams.map((team: any, index: number) =>
      FixtureFactory.build({
        fixtureId: `fixture-${index}`,
        gameworldId: team.gameworldId,
        date: Date.now() + index * 24 * 60 * 60 * 1000, // Spread over days
      })
    );
  }),
}));

describe('FixtureGenerationService', () => {
  let service: FixtureGenerationService;
  let repositories: ReturnType<typeof createMockRepositories>;

  beforeEach(() => {
    repositories = createMockRepositories();
    service = new FixtureGenerationService(repositories);
  });

  describe('prepareFixtureGenerationData', () => {
    it('should successfully prepare fixture generation data', async () => {
      // Arrange
      const gameworldId = 'gameworld-1';
      const leagueId = 'league-1';
      const teams = [
        TeamsFactory.build({ teamId: 'team-1', gameworldId }),
        TeamsFactory.build({ teamId: 'team-2', gameworldId }),
      ];

      mockTeamRepository.getTeamsByLeague.mockResolvedValue(teams);

      // Act
      const result = await service.prepareFixtureGenerationData(gameworldId, leagueId);

      // Assert
      expect(result).toBeDefined();
      expect(result.gameworldId).toBe(gameworldId);
      expect(result.leagueId).toBe(leagueId);
      expect(result.teams).toEqual(teams);
      expect(result.fixtures).toHaveLength(2);
    });

    it('should throw error when no teams found', async () => {
      // Arrange
      const gameworldId = 'gameworld-1';
      const leagueId = 'league-1';
      mockTeamRepository.getTeamsByLeague.mockResolvedValue([]);

      // Act & Assert
      await expect(service.prepareFixtureGenerationData(gameworldId, leagueId)).rejects.toThrow(
        'No teams found for league: league-1 in gameworld: gameworld-1'
      );
    });

    it('should throw error when insufficient teams', async () => {
      // Arrange
      const gameworldId = 'gameworld-1';
      const leagueId = 'league-1';
      const teams = [TeamsFactory.build({ teamId: 'team-1', gameworldId })];

      mockTeamRepository.getTeamsByLeague.mockResolvedValue(teams);

      // Act & Assert
      await expect(service.prepareFixtureGenerationData(gameworldId, leagueId)).rejects.toThrow(
        'Need at least 2 teams to generate fixtures. Found 1 teams in league: league-1'
      );
    });
  });

  describe('validateFixtureData', () => {
    it('should pass validation for valid fixture data', () => {
      // Arrange
      const data = {
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        teams: [TeamsFactory.build(), TeamsFactory.build()],
        fixtures: [
          FixtureFactory.build({
            fixtureId: 'fixture-1',
            gameworldId: 'gameworld-1',
            date: Date.now(),
          }),
        ],
      };

      // Act & Assert
      expect(() => service.validateFixtureData(data)).not.toThrow();
    });

    it('should throw error for missing identifiers', () => {
      // Arrange
      const data = {
        gameworldId: '',
        leagueId: 'league-1',
        teams: [TeamsFactory.build(), TeamsFactory.build()],
        fixtures: [FixtureFactory.build()],
      };

      // Act & Assert
      expect(() => service.validateFixtureData(data)).toThrow(
        'Missing required identifiers for fixture generation'
      );
    });

    it('should throw error for insufficient teams', () => {
      // Arrange
      const data = {
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        teams: [TeamsFactory.build()],
        fixtures: [FixtureFactory.build()],
      };

      // Act & Assert
      expect(() => service.validateFixtureData(data)).toThrow(
        'Insufficient teams for fixture generation: 1'
      );
    });

    it('should throw error for no fixtures generated', () => {
      // Arrange
      const data = {
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        teams: [TeamsFactory.build(), TeamsFactory.build()],
        fixtures: [],
      };

      // Act & Assert
      expect(() => service.validateFixtureData(data)).toThrow('No fixtures were generated');
    });

    it('should throw error for fixture with wrong gameworld', () => {
      // Arrange
      const data = {
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        teams: [TeamsFactory.build(), TeamsFactory.build()],
        fixtures: [
          FixtureFactory.build({
            fixtureId: 'fixture-1',
            gameworldId: 'wrong-gameworld',
            date: Date.now(),
          }),
        ],
      };

      // Act & Assert
      expect(() => service.validateFixtureData(data)).toThrow(
        'Fixture gameworld mismatch: expected gameworld-1, got wrong-gameworld'
      );
    });
  });

  describe('calculateSeasonEndDate', () => {
    it('should return the latest fixture date', () => {
      // Arrange
      const fixtures = [
        FixtureFactory.build({ date: 1000 }),
        FixtureFactory.build({ date: 3000 }),
        FixtureFactory.build({ date: 2000 }),
      ];

      // Act
      const result = service.calculateSeasonEndDate(fixtures);

      // Assert
      expect(result).toBe(3000);
    });

    it('should return current time for empty fixtures', () => {
      // Arrange
      const fixtures: any[] = [];
      const beforeTime = Date.now();

      // Act
      const result = service.calculateSeasonEndDate(fixtures);

      // Assert
      const afterTime = Date.now();
      expect(result).toBeGreaterThanOrEqual(beforeTime);
      expect(result).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('getFixtureStatistics', () => {
    it('should return correct statistics for fixtures', () => {
      // Arrange
      const teams = [TeamsFactory.build(), TeamsFactory.build()];
      const fixtures = [
        FixtureFactory.build({ date: 1000 }),
        FixtureFactory.build({ date: 3000 }),
        FixtureFactory.build({ date: 2000 }),
      ];
      const data = { teams, fixtures } as any;

      // Act
      const result = service.getFixtureStatistics(data);

      // Assert
      expect(result).toEqual({
        teamCount: 2,
        fixtureCount: 3,
        firstFixtureDate: 1000,
        lastFixtureDate: 3000,
        seasonDuration: 2000,
      });
    });

    it('should handle empty fixtures', () => {
      // Arrange
      const teams = [TeamsFactory.build(), TeamsFactory.build()];
      const fixtures: any[] = [];
      const data = { teams, fixtures } as any;

      // Act
      const result = service.getFixtureStatistics(data);

      // Assert
      expect(result).toEqual({
        teamCount: 2,
        fixtureCount: 0,
        firstFixtureDate: 0,
        lastFixtureDate: 0,
        seasonDuration: 0,
      });
    });
  });

  describe('prepareRetryInfo', () => {
    it('should prepare retry info for retryable error', () => {
      // Arrange
      const error = new Error('Connection timeout');

      // Act
      const result = service.prepareRetryInfo('gameworld-1', 'league-1', error, 1);

      // Assert
      expect(result.shouldRetry).toBe(true);
      expect(result.nextRetryDelay).toBe(30000); // 30 seconds for first retry
      expect(result.retryPayload).toEqual({
        gameworldId: 'gameworld-1',
        leagueId: 'league-1',
        attempt: 2,
        originalError: 'Connection timeout',
        retryAt: expect.any(Number),
      });
    });

    it('should not retry after max attempts', () => {
      // Arrange
      const error = new Error('Connection timeout');

      // Act
      const result = service.prepareRetryInfo('gameworld-1', 'league-1', error, 3);

      // Assert
      expect(result.shouldRetry).toBe(false);
    });

    it('should not retry non-retryable errors', () => {
      // Arrange
      const error = new Error('Invalid data format');

      // Act
      const result = service.prepareRetryInfo('gameworld-1', 'league-1', error, 1);

      // Assert
      expect(result.shouldRetry).toBe(false);
    });
  });
});
