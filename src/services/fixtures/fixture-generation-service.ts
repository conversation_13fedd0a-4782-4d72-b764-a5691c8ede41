import { Fixture } from '@/entities/Fixture.js';
import { Team } from '@/entities/Team.js';
import { generateFixtures } from '@/functions/generate/fixtures.js';
import { Repositories } from '@/middleware/database/types.js';
import { logger } from '@/utils/logger.js';

export interface FixtureGenerationData {
  gameworldId: string;
  leagueId: string;
  teams: Team[];
  fixtures: Fixture[];
}

/**
 * Service responsible for fixture generation logic and validation
 */
export class FixtureGenerationService {
  constructor(private repositories: Repositories) {}

  /**
   * Prepare all data needed for fixture generation
   */
  async prepareFixtureGenerationData(
    gameworldId: string,
    leagueId: string
  ): Promise<FixtureGenerationData> {
    const correlationId = `fixture-gen-${gameworldId}-${leagueId}-${Date.now()}`;

    logger.debug('Preparing fixture generation data', {
      gameworldId,
      leagueId,
      correlationId,
    });

    // Get teams for the league
    const teams = await this.repositories.teamRepository.getTeamsByLeague(leagueId);

    logger.debug('Teams retrieved for fixture generation', {
      leagueId,
      gameworldId,
      teamCount: teams.length,
      correlationId,
    });

    if (teams.length === 0) {
      throw new Error(`No teams found for league: ${leagueId} in gameworld: ${gameworldId}`);
    }

    if (teams.length < 2) {
      throw new Error(
        `Need at least 2 teams to generate fixtures. Found ${teams.length} teams in league: ${leagueId}`
      );
    }

    // Generate fixtures using the existing logic
    const fixtures = generateFixtures(
      this.repositories.teamRepository,
      this.repositories.leagueRepository,
      teams
    );

    logger.debug('Fixtures generated successfully', {
      leagueId,
      gameworldId,
      fixtureCount: fixtures.length,
      correlationId,
    });

    return {
      gameworldId,
      leagueId,
      teams,
      fixtures,
    };
  }

  /**
   * Validate fixture generation data
   */
  validateFixtureData(data: FixtureGenerationData): void {
    const { gameworldId, leagueId, teams, fixtures } = data;

    if (!gameworldId || !leagueId) {
      throw new Error('Missing required identifiers for fixture generation');
    }

    if (teams.length < 2) {
      throw new Error(`Insufficient teams for fixture generation: ${teams.length}`);
    }

    if (fixtures.length === 0) {
      throw new Error('No fixtures were generated');
    }

    // Validate that all fixtures have required properties
    for (const fixture of fixtures) {
      if (!fixture.fixtureId || !fixture.homeTeam || !fixture.awayTeam || !fixture.date) {
        throw new Error('Generated fixture is missing required properties');
      }

      if (fixture.gameworldId !== gameworldId) {
        throw new Error(
          `Fixture gameworld mismatch: expected ${gameworldId}, got ${fixture.gameworldId}`
        );
      }
    }

    logger.debug('Fixture data validation passed', {
      gameworldId,
      leagueId,
      teamCount: teams.length,
      fixtureCount: fixtures.length,
    });
  }

  /**
   * Calculate the season end date based on fixtures
   */
  calculateSeasonEndDate(fixtures: Fixture[]): number {
    if (fixtures.length === 0) {
      return Date.now();
    }

    return fixtures.reduce((maxDate, fixture) => {
      return Math.max(maxDate, fixture.date);
    }, 0);
  }

  /**
   * Get fixture generation statistics for logging
   */
  getFixtureStatistics(data: FixtureGenerationData): {
    teamCount: number;
    fixtureCount: number;
    firstFixtureDate: number;
    lastFixtureDate: number;
    seasonDuration: number;
  } {
    const { teams, fixtures } = data;

    if (fixtures.length === 0) {
      return {
        teamCount: teams.length,
        fixtureCount: 0,
        firstFixtureDate: 0,
        lastFixtureDate: 0,
        seasonDuration: 0,
      };
    }

    const dates = fixtures.map((f) => f.date).sort((a, b) => a - b);
    const firstDate = dates[0]!;
    const lastDate = dates[dates.length - 1]!;

    return {
      teamCount: teams.length,
      fixtureCount: fixtures.length,
      firstFixtureDate: firstDate,
      lastFixtureDate: lastDate,
      seasonDuration: lastDate - firstDate,
    };
  }

  /**
   * Prepare retry information for failed fixture generation
   */
  prepareRetryInfo(
    gameworldId: string,
    leagueId: string,
    error: Error,
    attempt: number = 1
  ): {
    retryPayload: any;
    shouldRetry: boolean;
    nextRetryDelay: number;
  } {
    const maxRetries = 3;
    const baseDelay = 30000; // 30 seconds
    const shouldRetry = attempt < maxRetries && this.isRetryableError(error);
    const nextRetryDelay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff

    return {
      retryPayload: {
        gameworldId,
        leagueId,
        attempt: attempt + 1,
        originalError: error.message,
        retryAt: Date.now() + nextRetryDelay,
      },
      shouldRetry,
      nextRetryDelay,
    };
  }

  /**
   * Determine if an error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'timeout',
      'connection',
      'network',
      'temporary',
      'throttle',
      'rate limit',
    ];

    const errorMessage = error.message.toLowerCase();
    return retryableErrors.some((retryableError) => errorMessage.includes(retryableError));
  }
}
