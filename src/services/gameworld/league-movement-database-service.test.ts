import { LeagueMovementDatabaseService } from '@/services/gameworld/league-movement-database-service.js';
import { SQS } from '@/services/sqs/sqs.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';
import { ManagerFactory } from '@/testing/factories/managerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { beforeEach, describe, expect, it, MockInstance, vi } from 'vitest';

// Mock external dependencies
vi.mock('@/utils/lambda.js', () => ({
  Lambda: vi.fn().mockImplementation(() => ({
    eventInvoke: vi.fn(),
  })),
}));

describe('LeagueMovementDatabaseService', () => {
  let mockSqsSendBatch: MockInstance;
  let service: LeagueMovementDatabaseService;

  beforeEach(() => {
    service = new LeagueMovementDatabaseService();

    // Get the mocked SQS instance
    mockSqsSendBatch = vi.spyOn(SQS.prototype, 'sendBatch').mockResolvedValue();

    // Set up environment variables
    process.env.GENERATE_PLAYERS_QUEUE_URL =
      'https://sqs.us-east-1.amazonaws.com/123456789012/generate-players';
    process.env.FIXTURE_GENERATION_QUEUE_URL =
      'https://sqs.us-east-1.amazonaws.com/123456789012/generate-players';
  });

  describe('executeLeagueMovement', () => {
    it('should execute all league movement operations', async () => {
      // Arrange
      const mockEm = {
        nativeUpdate: vi.fn(),
        nativeDelete: vi.fn(),
        find: vi.fn(),
        insertMany: vi.fn(),
      } as any;

      const mockLeagueMovementService = {
        awardPrizeMoney: vi.fn(),
        manageAvailableTeams: vi.fn(),
        optimizeFixtureDeletion: vi.fn(),
      };

      // Provide minimal valid data
      const league = { id: 'league-1' };
      const team = TeamsFactory.build({ teamId: 'team-1', league: league as any });
      const movement = { teamId: 'team-1', fromLeagueId: 'league-1', toLeagueId: 'league-2' };
      const movementData = {
        gameworldId: 'gw-1',
        leagues: [league],
        allTeams: [team],
        movements: [movement],
        sortedLeagues: new Map([[league.id, [team]]]),
      };

      // Act
      await service.executeLeagueMovement(mockEm, movementData, mockLeagueMovementService);

      // Assert
      expect(mockLeagueMovementService.awardPrizeMoney).toHaveBeenCalledWith(
        mockEm,
        movementData.sortedLeagues,
        movementData.leagues
      );
      expect(mockLeagueMovementService.manageAvailableTeams).toHaveBeenCalledWith(
        mockEm,
        movementData.gameworldId,
        movementData.movements,
        movementData.allTeams,
        movementData.leagues
      );
      expect(mockLeagueMovementService.optimizeFixtureDeletion).toHaveBeenCalledWith(
        mockEm,
        movementData.gameworldId,
        movementData.leagues
      );
    });
  });

  describe('updateTeamLeaguesOptimized', () => {
    it('should update moving teams with new league and reset standings', async () => {
      // Arrange
      const mockEm = {
        nativeUpdate: vi.fn(),
      } as any;

      const teams = [
        TeamsFactory.build({ teamId: 'team-1', gameworldId: 'gw-1' }),
        TeamsFactory.build({ teamId: 'team-2', gameworldId: 'gw-1' }),
      ];

      const movements = [
        {
          teamId: 'team-1',
          fromLeagueId: 'league-1',
          toLeagueId: 'league-2',
        },
      ];

      // Act
      await (service as any).updateTeamLeaguesOptimized(mockEm, teams, movements, 'gw-1');

      // Assert
      // Should update moving team with new league and reset standings
      expect(mockEm.nativeUpdate).toHaveBeenCalledWith(
        expect.anything(),
        { teamId: 'team-1', gameworldId: 'gw-1' },
        {
          league: 'league-2',
          points: 0,
          goalsFor: 0,
          goalsAgainst: 0,
          wins: 0,
          draws: 0,
          losses: 0,
          played: 0,
        }
      );

      // Should reset standings for stationary team
      expect(mockEm.nativeUpdate).toHaveBeenCalledWith(
        expect.anything(),
        { teamId: { $in: ['team-2'] }, gameworldId: 'gw-1' },
        {
          points: 0,
          goalsFor: 0,
          goalsAgainst: 0,
          wins: 0,
          draws: 0,
          losses: 0,
          played: 0,
        }
      );
    });

    it('should handle teams with no movements', async () => {
      // Arrange
      const mockEm = {
        nativeUpdate: vi.fn(),
      } as any;

      const teams = [TeamsFactory.build({ teamId: 'team-1', gameworldId: 'gw-1' })];

      const movements: any[] = [];

      // Act
      await (service as any).updateTeamLeaguesOptimized(mockEm, teams, movements, 'gw-1');

      // Assert
      // Should only reset standings for stationary teams
      expect(mockEm.nativeUpdate).toHaveBeenCalledTimes(1);
      expect(mockEm.nativeUpdate).toHaveBeenCalledWith(
        expect.anything(),
        { teamId: { $in: ['team-1'] }, gameworldId: 'gw-1' },
        {
          points: 0,
          goalsFor: 0,
          goalsAgainst: 0,
          wins: 0,
          draws: 0,
          losses: 0,
          played: 0,
        }
      );
    });
  });

  describe('generateFixturesForAllLeagues', () => {
    it('should send fixture generation requests to SQS for all leagues', async () => {
      // Arrange
      const leagues = [
        {
          id: 'league-1',
          gameworld: { id: 'gw-1' },
        },
        {
          id: 'league-2',
          gameworld: { id: 'gw-1' },
        },
      ];

      // Act
      await service.generateFixturesForAllLeagues(leagues);

      // Assert
      expect(mockSqsSendBatch).toHaveBeenCalledTimes(1);
      expect(mockSqsSendBatch).toHaveBeenCalledWith(
        'https://sqs.us-east-1.amazonaws.com/123456789012/generate-players',
        [
          {
            Id: 'league-1',
            MessageBody: expect.stringContaining('"leagueId":"league-1"'),
          },
          {
            Id: 'league-2',
            MessageBody: expect.stringContaining('"leagueId":"league-2"'),
          },
        ]
      );
    });

    it('should throw error when FIXTURE_GENERATION_QUEUE_URL is not set', async () => {
      // Arrange
      delete process.env.FIXTURE_GENERATION_QUEUE_URL;

      // Act & Assert
      await expect(service.generateFixturesForAllLeagues([])).rejects.toThrow(
        'FIXTURE_GENERATION_QUEUE_URL environment variable not set'
      );
    });
  });

  describe('processYouthPlayerRequests', () => {
    it('should send youth player requests to SQS queue', async () => {
      // Arrange
      const league = LeagueFactory.build({ id: 'league-1' });
      const manager = ManagerFactory.build({ managerId: 'manager-1' });
      const teams = [
        TeamsFactory.build({
          teamId: 'team-1',
          gameworldId: 'gw-1',
          league: league,
          tier: 3,
          manager: manager,
        }),
        TeamsFactory.build({
          teamId: 'team-2',
          gameworldId: 'gw-1',
          league: league,
          tier: 3,
          manager: undefined,
        }),
      ];

      const sortedLeagues = new Map([['league-1', teams]]);

      // Act
      await service.processYouthPlayerRequests(sortedLeagues);

      // Assert
      expect(mockSqsSendBatch).toHaveBeenCalledWith(
        'https://sqs.us-east-1.amazonaws.com/123456789012/generate-players',
        [
          {
            Id: 'team-1',
            MessageBody: JSON.stringify({
              gameworldId: 'gw-1',
              leagueId: 'league-1',
              tier: 3,
              teamId: 'team-1',
              managerId: 'manager-1',
              requiredPlayers: 3,
            }),
          },
          {
            Id: 'team-2',
            MessageBody: JSON.stringify({
              gameworldId: 'gw-1',
              leagueId: 'league-1',
              tier: 3,
              teamId: 'team-2',
              managerId: undefined,
              requiredPlayers: 3,
            }),
          },
        ]
      );
    });

    it('should handle empty teams list', async () => {
      // Arrange
      const sortedLeagues = new Map();

      // Act
      await service.processYouthPlayerRequests(sortedLeagues);

      // Assert
      expect(mockSqsSendBatch).not.toHaveBeenCalled();
    });

    it('should throw error when GENERATE_PLAYERS_QUEUE_URL is not set', async () => {
      // Arrange
      delete process.env.GENERATE_PLAYERS_QUEUE_URL;
      const teams = [TeamsFactory.build()];
      const sortedLeagues = new Map([['league-1', teams]]);

      // Act & Assert
      await expect(service.processYouthPlayerRequests(sortedLeagues)).rejects.toThrow(
        'GENERATE_PLAYERS_QUEUE_URL environment variable not set'
      );
    });
  });
});
