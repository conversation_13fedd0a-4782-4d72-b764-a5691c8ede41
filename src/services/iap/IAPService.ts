import { Manager } from '@/entities/Manager.js';
import { Purchases, PurchaseStatus, PurchaseType } from '@/entities/Purchases.js';
import { TeamTrainingSlot } from '@/entities/TeamTrainingSlot.js';
import { RevenueCatEventType, WebhookEvent } from '@/functions/iap/types.js';
import { Repositories } from '@/middleware/database/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { logger } from '@/utils/logger.js';

export interface ProductConfig {
  productId: string;
  type: PurchaseType;
  rewards?: {
    magicSponges?: number;
    scoutTokens?: number;
    superScoutTokens?: number;
    cardAppeals?: number;
    trainingBoosts?: number;
    trainingSlot?: number;
  };
  isRecurring?: boolean;
}

export class IAPService {
  private static readonly PRODUCT_CONFIGS: ProductConfig[] = [
    {
      productId: 'jfg_sweetfa_1',
      type: PurchaseType.CONSUMABLE,
      // No rewards for this consumable
    },
    {
      productId: 'jfg_superfan_1:sf1',
      type: PurchaseType.SUBSCRIPTION,
      rewards: {
        magicSponges: 5, // Monthly reward
        cardAppeals: 3,
        superScoutTokens: 5,
      },
      isRecurring: true,
    },
    {
      productId: 'jfg_adfree_1',
      type: PurchaseType.NON_RENEWING,
      // No backend rewards needed - handled client-side
    },
    {
      productId: 'super_scout_token_5',
      type: PurchaseType.CONSUMABLE,
      rewards: {
        superScoutTokens: 5,
      },
    },
    {
      productId: 'jfg_training_slot_5',
      type: PurchaseType.NON_RENEWING,
      rewards: {
        trainingSlot: 4, // slot index 4 is the 5th slot
      },
    },
    {
      productId: 'jfg_redcardappeal_3',
      type: PurchaseType.CONSUMABLE,
      rewards: {
        cardAppeals: 3,
      },
    },
    {
      productId: 'jfg_magicsponge_5',
      type: PurchaseType.CONSUMABLE,
      rewards: {
        magicSponges: 5,
      },
    },
  ];

  constructor(private repositories: Repositories) {}

  async processWebhookEvent(event: WebhookEvent): Promise<void> {
    const { type, app_user_id } = event.event;

    logger.info('Processing IAP webhook event', {
      eventType: type,
      userId: app_user_id,
      eventId: event.event.id,
    });

    // Get the manager
    const manager = await this.repositories.managerRepository.getManagerById(app_user_id);
    if (!manager) {
      logger.error('Manager not found for IAP event', { userId: app_user_id });
      throw new Error(`Manager with ID ${app_user_id} not found`);
    }

    switch (type) {
      case RevenueCatEventType.INITIAL_PURCHASE:
        await this.handleInitialPurchase(event, manager);
        break;
      case RevenueCatEventType.RENEWAL:
        await this.handleRenewal(event, manager);
        break;
      case RevenueCatEventType.CANCELLATION:
        await this.handleCancellation(event, manager);
        break;
      case RevenueCatEventType.UNCANCELLATION:
        await this.handleUncancellation(event, manager);
        break;
      case RevenueCatEventType.NON_RENEWING_PURCHASE:
        await this.handleNonRenewingPurchase(event, manager);
        break;
      case RevenueCatEventType.SUBSCRIPTION_PAUSED:
        await this.handleSubscriptionPaused(event, manager);
        break;
      case RevenueCatEventType.BILLING_ISSUE:
        await this.handleBillingIssue(event, manager);
        break;
      case RevenueCatEventType.EXPIRATION:
        await this.handleExpiration(event, manager);
        break;
      case RevenueCatEventType.REFUND:
        await this.handleRefund(event, manager);
        break;
      case RevenueCatEventType.REFUND_REVERSED:
        await this.handleRefundReversed(event, manager);
        break;
      case RevenueCatEventType.PRODUCT_CHANGE:
        await this.handleProductChange(event, manager);
        break;
      case RevenueCatEventType.SUBSCRIPTION_EXTENDED:
        await this.handleSubscriptionExtended(event, manager);
        break;
      case RevenueCatEventType.TRANSFER:
        await this.handleTransfer(event, manager);
        break;
      default:
        logger.warn('Unhandled IAP event type', { eventType: type });
    }
  }

  private async handleInitialPurchase(event: WebhookEvent, manager: Manager): Promise<void> {
    const purchase = this.createPurchaseFromEvent(event, manager, PurchaseStatus.ACTIVE);
    await this.repositories.purchaseRepository.upsertPurchase(purchase);

    // Award initial rewards if applicable
    await this.awardRewards(event.event.product_id!, manager);

    // Send notification
    await this.sendPurchaseNotification(manager, event.event.product_id!, 'purchase_confirmed');
  }

  private async handleRenewal(event: WebhookEvent, manager: Manager): Promise<void> {
    const purchase = this.createPurchaseFromEvent(event, manager, PurchaseStatus.ACTIVE);
    await this.repositories.purchaseRepository.upsertPurchase(purchase);

    // Award renewal rewards if applicable
    await this.awardRewards(event.event.product_id!, manager);

    // Send notification
    await this.sendPurchaseNotification(manager, event.event.product_id!, 'subscription_renewed');
  }

  private async handleCancellation(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.CANCELLED,
      event.event.expiration_at_ms || undefined,
      event.event.cancel_reason
    );

    // Send notification
    await this.sendPurchaseNotification(manager, event.event.product_id!, 'subscription_cancelled');
  }

  private async handleUncancellation(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.ACTIVE,
      event.event.expiration_at_ms || undefined
    );

    // Send notification
    await this.sendPurchaseNotification(
      manager,
      event.event.product_id!,
      'subscription_reactivated'
    );
  }

  private async handleNonRenewingPurchase(event: WebhookEvent, manager: Manager): Promise<void> {
    const purchase = this.createPurchaseFromEvent(event, manager, PurchaseStatus.ACTIVE);
    await this.repositories.purchaseRepository.upsertPurchase(purchase);

    // Award rewards if applicable
    await this.awardRewards(event.event.product_id!, manager);

    // Send notification
    await this.sendPurchaseNotification(manager, event.event.product_id!, 'purchase_confirmed');
  }

  private async handleSubscriptionPaused(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.PAUSED,
      event.event.expiration_at_ms || undefined
    );
  }

  private async handleBillingIssue(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.BILLING_ISSUE,
      event.event.expiration_at_ms || undefined
    );

    // Send notification about billing issue
    await this.sendPurchaseNotification(manager, event.event.product_id!, 'billing_issue');
  }

  private async handleExpiration(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.EXPIRED,
      event.event.expiration_at_ms || undefined,
      undefined,
      event.event.expiration_reason
    );

    // Send notification
    await this.sendPurchaseNotification(manager, event.event.product_id!, 'subscription_expired');
  }

  private async handleRefund(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.REFUNDED,
      event.event.expiration_at_ms || undefined,
      event.event.cancel_reason
    );

    // Remove rewards if applicable
    await this.removeRewards(event.event.product_id!, manager);

    // Send notification
    await this.sendPurchaseNotification(manager, event.event.product_id!, 'refund_processed');
  }

  private async handleRefundReversed(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.ACTIVE,
      event.event.expiration_at_ms || undefined
    );

    // Re-award rewards if applicable
    await this.awardRewards(event.event.product_id!, manager);
  }

  private async handleProductChange(event: WebhookEvent, manager: Manager): Promise<void> {
    // Handle product changes - this is complex and may require custom logic
    logger.info('Product change event received', {
      oldProduct: event.event.product_id,
      newProduct: event.event.new_product_id,
      managerId: manager.managerId,
    });
  }

  private async handleSubscriptionExtended(event: WebhookEvent, manager: Manager): Promise<void> {
    if (!event.event.transaction_id) return;

    await this.repositories.purchaseRepository.updatePurchaseStatus(
      event.event.transaction_id,
      PurchaseStatus.ACTIVE,
      event.event.expiration_at_ms || undefined
    );
  }

  private async handleTransfer(event: WebhookEvent, manager: Manager): Promise<void> {
    // Handle account transfers - may need special logic
    logger.info('Transfer event received', {
      transferredFrom: event.event.transferred_from,
      transferredTo: event.event.transferred_to,
      managerId: manager.managerId,
    });
  }

  private createPurchaseFromEvent(
    event: WebhookEvent,
    manager: Manager,
    status: PurchaseStatus
  ): Purchases {
    const productConfig = IAPService.getProductConfig(event.event.product_id!);

    return new Purchases({
      manager: manager,
      productId: event.event.product_id!,
      transactionId: event.event.transaction_id!,
      originalTransactionId: event.event.original_transaction_id!,
      revenueCatEventId: event.event.id,
      store: event.event.store!,
      environment: event.event.environment,
      purchaseType: productConfig?.type || PurchaseType.NON_RENEWING,
      status,
      purchasedAt: event.event.purchased_at_ms!,
      expirationAt: event.event.expiration_at_ms || undefined,
      currency: event.event.currency!,
      price: event.event.price!,
      priceInPurchasedCurrency: event.event.price_in_purchased_currency!,
      countryCode: event.event.country_code,
      entitlementIds: event.event.entitlement_ids,
      cancelReason: event.event.cancel_reason,
      expirationReason: event.event.expiration_reason,
      rawEventData: event.event,
    });
  }

  public static getProductConfig(productId: string): ProductConfig | undefined {
    return IAPService.PRODUCT_CONFIGS.find((config) => config.productId === productId);
  }

  private async awardRewards(productId: string, manager: Manager): Promise<void> {
    const productConfig = IAPService.getProductConfig(productId);
    if (!productConfig?.rewards) {
      logger.debug('No rewards configured for product', { productId });
      return;
    }

    const { rewards } = productConfig;
    const updates: Partial<Manager> = {};

    if (rewards.magicSponges) {
      await this.repositories.managerRepository.updateMagicSpongeCount(
        manager.managerId,
        rewards.magicSponges
      );
    }

    if (rewards.cardAppeals) {
      await this.repositories.managerRepository.updateCardAppealCount(
        manager.managerId,
        rewards.cardAppeals
      );
    }

    if (rewards.scoutTokens) {
      updates.scoutTokens = (manager.scoutTokens || 0) + rewards.scoutTokens;
    }

    if (rewards.superScoutTokens) {
      updates.superScoutTokens = (manager.superScoutTokens || 0) + rewards.superScoutTokens;
    }

    if (rewards.trainingBoosts) {
      updates.trainingBoosts = (manager.trainingBoosts || 0) + rewards.trainingBoosts;
    }

    if (rewards.trainingSlot) {
      const slot = new TeamTrainingSlot();
      slot.team = this.repositories.teamRepository.createFromPK(manager.team?.teamId!);
      slot.slotIndex = rewards.trainingSlot;
      await this.repositories.trainingRepository.createSlot(slot);
    }

    if (Object.keys(updates).length > 0) {
      await this.repositories.managerRepository.updateManagerById(manager.managerId, updates);
    }

    logger.info('Rewards awarded for purchase', {
      managerId: manager.managerId,
      productId,
      rewards,
    });
  }

  private async removeRewards(productId: string, manager: Manager): Promise<void> {
    const productConfig = IAPService.getProductConfig(productId);
    if (!productConfig?.rewards) {
      logger.debug('No rewards to remove for product', { productId });
      return;
    }

    const { rewards } = productConfig;
    const updates: Partial<Manager> = {};

    // Remove rewards (but don't go below 0)
    if (rewards.magicSponges) {
      const currentSponges = manager.magicSponges || 0;
      const newCount = currentSponges - rewards.magicSponges;
      await this.repositories.managerRepository.updateMagicSpongeCount(
        manager.managerId,
        Math.max(0, newCount - currentSponges) // This will be negative or zero
      );
    }

    if (rewards.cardAppeals) {
      const currentAppeals = manager.cardAppeals || 0;
      const newCount = Math.max(0, currentAppeals - rewards.cardAppeals);
      await this.repositories.managerRepository.updateCardAppealCount(
        manager.managerId,
        newCount - currentAppeals // This will be negative or zero
      );
    }

    if (rewards.scoutTokens) {
      updates.scoutTokens = Math.max(0, (manager.scoutTokens || 0) - rewards.scoutTokens);
    }

    if (rewards.superScoutTokens) {
      updates.superScoutTokens = Math.max(
        0,
        (manager.superScoutTokens || 0) - rewards.superScoutTokens
      );
    }

    if (rewards.trainingBoosts) {
      updates.trainingBoosts = Math.max(0, (manager.trainingBoosts || 0) - rewards.trainingBoosts);
    }

    if (rewards.trainingSlot) {
      // Remove the training slot
      await this.repositories.trainingRepository.lockIAPSlot(manager.team?.teamId!);
    }

    if (Object.keys(updates).length > 0) {
      await this.repositories.managerRepository.updateManagerById(manager.managerId, updates);
    }

    logger.info('Rewards removed due to refund', {
      managerId: manager.managerId,
      productId,
      rewards,
    });
  }

  private async sendPurchaseNotification(
    manager: Manager,
    productId: string,
    notificationType: string
  ): Promise<void> {
    try {
      const notificationManager = NotificationManager.getInstance();
      notificationManager.assignManagerPreferences(manager, this.repositories);

      // You can customize these notifications based on the product and notification type
      const productConfig = IAPService.getProductConfig(productId);
      const productName = this.getProductDisplayName(productId);

      switch (notificationType) {
        case 'purchase_confirmed':
          // Implementation would depend on your notification requirements
          logger.info('Purchase confirmed notification sent', {
            managerId: manager.managerId,
            productId,
          });
          break;
        case 'subscription_renewed':
          logger.info('Subscription renewed notification sent', {
            managerId: manager.managerId,
            productId,
          });
          break;
        case 'subscription_cancelled':
          logger.info('Subscription cancelled notification sent', {
            managerId: manager.managerId,
            productId,
          });
          break;
        // Add more notification types as needed
      }
    } catch (error) {
      logger.error('Failed to send purchase notification', {
        error,
        managerId: manager.managerId,
        productId,
        notificationType,
      });
      // Don't throw - notification failure shouldn't break purchase processing
    }
  }

  private getProductDisplayName(productId: string): string {
    switch (productId) {
      case 'jfg_sweetfa_1':
        return 'Sweet FA Pack';
      case 'jfg_superfan_1:sf1':
        return 'Super Fan Subscription';
      case 'jfg_adfree_1':
        return 'Ad-Free Experience';
      default:
        return productId;
    }
  }
}
