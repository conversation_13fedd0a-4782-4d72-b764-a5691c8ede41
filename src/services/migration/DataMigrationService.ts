import { Manager } from '@/entities/Manager.js';
import { ManagerRepository } from '@/storage-interface/managers/manager-repository.interface.js';
import { TeamRepository } from '@/storage-interface/teams/team-repository.interface.js';
import { logger } from '@/utils/logger.js';

export interface MigrationConflictData {
  hasConflicts: boolean;
  guestManager?: Manager;
  authenticatedManager?: Manager;
}

export interface MigrationResolution {
  keepGuestData: boolean;
  guestUserId: string;
  authenticatedUserId: string;
}

export class DataMigrationService {
  constructor(
    private managerRepository: ManagerRepository,
    private teamRepository: TeamRepository
  ) {}

  /**
   * Check if there are conflicts between guest and authenticated user data
   */
  async checkMigrationConflicts(
    guestUserId: string,
    authenticatedUserId: string
  ): Promise<MigrationConflictData> {
    try {
      // Get both managers
      const guestManager = await this.managerRepository.getManagerById(guestUserId, true);
      const authenticatedManager = await this.managerRepository.getManagerById(
        authenticatedUserId,
        true
      );

      // Validate guest manager
      if (!guestManager || !guestManager.isGuest) {
        throw new Error('Guest user not found or not a guest account');
      }

      // If authenticated user doesn't have a team, no conflicts
      if (!authenticatedManager || !authenticatedManager.team) {
        return {
          hasConflicts: false,
        };
      }

      // Both users have teams, so there are conflicts
      return {
        hasConflicts: true,
        guestManager,
        authenticatedManager,
      };
    } catch (error) {
      logger.error('Error checking migration conflicts:', {
        error,
        guestUserId,
        authenticatedUserId,
      });
      throw error;
    }
  }

  /**
   * Resolve migration conflicts by keeping either guest or authenticated data
   */
  async resolveMigration(resolution: MigrationResolution): Promise<string> {
    const { keepGuestData, guestUserId, authenticatedUserId } = resolution;

    try {
      // Get both managers
      const guestManager = await this.managerRepository.getManagerById(guestUserId, true);
      const authenticatedManager = await this.managerRepository.getManagerById(
        authenticatedUserId,
        true
      );

      if (!guestManager || !guestManager.isGuest) {
        throw new Error('Guest user not found or not a guest account');
      }

      if (!authenticatedManager) {
        throw new Error('Authenticated user not found');
      }

      // Check if migration already completed
      /*if (authenticatedManager.migrationCompleted) {
        throw new Error('Migration already completed for this user');
      }*/

      let keptTeamId: string;

      if (keepGuestData) {
        keptTeamId = await this.migrateGuestDataToAuthenticated(guestManager, authenticatedManager);
      } else {
        keptTeamId = await this.keepAuthenticatedData(guestManager, authenticatedManager);
      }

      // Mark migration as completed
      authenticatedManager.migratedFromGuestId = guestUserId;
      authenticatedManager.migrationCompleted = true;

      // Save the authenticated manager
      await this.managerRepository.updateManager(authenticatedManager);

      // Delete the guest manager
      await this.managerRepository.deleteManager(guestUserId);

      logger.info('Migration completed successfully', {
        authenticatedUserId,
        guestUserId,
        keepGuestData,
        keptTeamId,
      });

      return keptTeamId;
    } catch (error) {
      logger.error('Error resolving migration:', {
        error,
        resolution,
      });
      throw error;
    }
  }

  /**
   * Migrate guest data to authenticated user account
   */
  private async migrateGuestDataToAuthenticated(
    guestManager: Manager,
    authenticatedManager: Manager
  ): Promise<string> {
    // Release authenticated user's current team if they have one
    if (authenticatedManager.team) {
      await this.teamRepository.releaseTeamToAvailable(
        authenticatedManager.team.teamId,
        authenticatedManager.team.gameworldId
      );
    }

    // Transfer guest team to authenticated user
    authenticatedManager.team = guestManager.team;
    authenticatedManager.gameworldId = guestManager.gameworldId;

    // Merge manager stats (take the maximum values for resources)
    authenticatedManager.scoutTokens = Math.max(
      authenticatedManager.scoutTokens,
      guestManager.scoutTokens
    );
    authenticatedManager.superScoutTokens = Math.max(
      authenticatedManager.superScoutTokens,
      guestManager.superScoutTokens
    );
    authenticatedManager.magicSponges = Math.max(
      authenticatedManager.magicSponges,
      guestManager.magicSponges
    );
    authenticatedManager.cardAppeals = Math.max(
      authenticatedManager.cardAppeals,
      guestManager.cardAppeals
    );
    authenticatedManager.trainingBoosts = Math.max(
      authenticatedManager.trainingBoosts,
      guestManager.trainingBoosts
    );
    authenticatedManager.loginStreak = Math.max(
      authenticatedManager.loginStreak,
      guestManager.loginStreak
    );

    authenticatedManager.wins = guestManager.wins;
    authenticatedManager.defeats = guestManager.defeats;
    authenticatedManager.trophies = guestManager.trophies || 0;

    return guestManager.team!.teamId;
  }

  /**
   * Keep authenticated user data and release guest team
   */
  private async keepAuthenticatedData(
    guestManager: Manager,
    authenticatedManager: Manager
  ): Promise<string> {
    // Release guest team back to available teams
    if (guestManager.team) {
      await this.teamRepository.releaseTeamToAvailable(
        guestManager.team.teamId,
        guestManager.team.gameworldId
      );
    }

    // Keep authenticated user's team
    return authenticatedManager.team!.teamId;
  }

  /**
   * Automatically migrate guest data when no conflicts exist
   */
  async autoMigrateGuestData(guestUserId: string, authenticatedUserId: string): Promise<void> {
    try {
      const conflicts = await this.checkMigrationConflicts(guestUserId, authenticatedUserId);

      if (!conflicts.hasConflicts && conflicts.guestManager) {
        // No conflicts, automatically migrate guest data
        await this.resolveMigration({
          keepGuestData: true,
          guestUserId,
          authenticatedUserId,
        });

        logger.info('Auto-migrated guest data (no conflicts)', {
          guestUserId,
          authenticatedUserId,
        });
      }
    } catch (error) {
      logger.error('Error auto-migrating guest data:', {
        error,
        guestUserId,
        authenticatedUserId,
      });
      // Don't throw - this is a best-effort operation
    }
  }
}
