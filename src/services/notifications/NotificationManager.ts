import { Manager } from '@/entities/Manager.js';
import { Player } from '@/entities/Player.ts';
import { PlayerAttributes } from '@/entities/PlayerAttributes.js';
import { Team } from '@/entities/Team.js';
import { TransferRequest } from '@/entities/TransferRequest.js';
import { Repositories } from '@/middleware/database/types.js';
import {
  NotificationCategory,
  NotificationChannel,
  NotificationPreferences,
} from '@/model/manager.js';
import { MatchReport } from '@/model/matchReport.js';
import { Auction } from '@/storage-interface/transfers/index.js';
import { EmailEvent } from '@/types/generated/email-event.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { Expo, ExpoPushMessage } from 'expo-server-sdk';
import { SQS } from '../sqs/sqs.ts';

type Notification = {
  email: {
    subject: string;
    content: string;
    title: string;
    template?: string;
  };
  push: {
    title: string;
    body: string;
  };
  inbox: {
    title: string;
    content: string;
  };
  category: NotificationCategory;
};

const sqs = new SQS({ tracer });

export class NotificationManager {
  private notificationPreferences: NotificationPreferences | undefined;
  private email: string | undefined;
  private pushToken: string | undefined;
  private currentManager: Manager | undefined;
  private repositories: Repositories | undefined;
  private static instance: NotificationManager;

  private constructor() {}

  static getInstance() {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  public async loadManagerPreferences(managerId: string, repositories: Repositories) {
    const manager = await repositories.managerRepository.getManagerById(managerId);
    if (!manager) {
      throw new Error(`Manager with ID ${managerId} not found`);
    }
    this.notificationPreferences = manager.notificationPreferences;
    this.email = manager.email;
    this.pushToken = manager.pushToken;
    this.currentManager = manager;
    this.repositories = repositories;
  }

  public assignManagerPreferences(manager: Manager, repositories?: Repositories) {
    this.notificationPreferences = manager.notificationPreferences;
    this.email = manager.email;
    this.pushToken = manager.pushToken;
    this.currentManager = manager;
    if (repositories) {
      this.repositories = repositories;
    }
  }

  private shouldSendNotification(
    prefs: NotificationPreferences,
    category: NotificationCategory,
    channel: NotificationChannel
  ): boolean {
    return Boolean(prefs?.[category]?.[channel]);
  }

  private async storeInInbox(notification: Notification) {
    try {
      // Only store in inbox if we have the repositories and manager context
      if (!this.repositories?.inboxRepository || !this.currentManager) {
        logger.debug('Skipping inbox storage - missing repository or manager context');
        return;
      }

      // Get team information from manager
      const gameworldId = this.currentManager.gameworldId;
      const teamId = this.currentManager.team?.teamId;

      if (!gameworldId || !teamId) {
        logger.warn('Cannot store in inbox - missing gameworldId or teamId', {
          managerId: this.currentManager.managerId,
          gameworldId,
          teamId,
        });
        return;
      }

      // Create inbox message with notification data
      const extra = JSON.stringify({
        category: notification.category,
        title: notification.inbox.title,
      });

      await this.repositories.inboxRepository.createMessage(
        gameworldId,
        teamId,
        Date.now(),
        notification.inbox.content,
        extra
      );

      logger.debug('Notification stored in inbox', {
        managerId: this.currentManager.managerId,
        gameworldId,
        teamId,
        category: notification.category,
      });
    } catch (error) {
      logger.error('Failed to store notification in inbox', {
        error,
        notification,
        managerId: this.currentManager?.managerId,
      });
      // Don't throw - inbox storage failure shouldn't break notification sending
    }
  }

  private async sendEmailNotification(notification: Notification) {
    try {
      const emailEvent: EmailEvent = {
        recipients: [this.email!],
        subject: notification.email.subject,
        content: notification.email.content,
        title: notification.email.title,
        template: notification.email.template || 'basic',
        managerId: this.currentManager!.managerId,
        category: notification.category,
      };

      await sqs.send(process.env.EMAIL_QUEUE_URL!, JSON.stringify(emailEvent));
    } catch (error) {
      logger.error('Failed to send email to queue', {
        error,
        notification,
      });
    }
  }

  private async sendPushNotification(notification: Notification) {
    try {
      // Check if we have a valid push token
      if (!this.pushToken) {
        logger.warn('No push token available for notification', { notification });
        return;
      }

      // Validate the push token
      if (!Expo.isExpoPushToken(this.pushToken)) {
        logger.error('Invalid Expo push token', { pushToken: this.pushToken });
        return;
      }

      // Create a new Expo SDK client
      const expo = new Expo();

      // Construct the message
      const message: ExpoPushMessage = {
        to: this.pushToken,
        sound: 'default',
        title: notification.push.title,
        body: notification.push.body,
        data: {
          category: notification.category,
          content: notification.push.body,
        },
        // Set priority to high to ensure timely delivery
        priority: 'high',
      };

      try {
        // Send the push notification
        const ticketChunk = await expo.sendPushNotificationsAsync([message]);
        const ticket = ticketChunk[0];

        if (!ticket) {
          logger.error('Failed to send push notification', {
            error: 'No ticket returned',
            notification,
          });
          return;
        }
        if (ticket.status === 'error') {
          logger.error('Error sending push notification', {
            error: ticket.message,
            details: ticket.details,
            notification,
          });

          // Handle specific error cases
          if (ticket.details && ticket.details.error === 'DeviceNotRegistered') {
            logger.warn('Device is no longer registered for push notifications', {
              pushToken: this.pushToken,
            });
            // In a real implementation, you might want to update the database to clear this token
          }
        } else {
          logger.info('Push notification sent successfully', {
            ticketId: ticket.id,
            category: notification.category,
          });
        }
      } catch (error) {
        logger.error('Failed to send push notification', {
          error,
          notification,
        });
      }
    } catch (error) {
      logger.error('Error in sendPushNotification', {
        error,
        notification,
      });
    }
  }

  private async sendNotification(notification: Notification) {
    // Always store in inbox first (regardless of notification preferences)
    await this.storeInInbox(notification);

    // Don't send notifications if we don't have preferences
    if (!this.notificationPreferences) {
      return;
    }

    const promises = [];

    if (
      this.email &&
      this.shouldSendNotification(
        this.notificationPreferences,
        notification.category,
        NotificationChannel.EMAIL
      )
    ) {
      promises.push(this.sendEmailNotification(notification));
    }
    if (
      this.pushToken &&
      this.shouldSendNotification(
        this.notificationPreferences,
        notification.category,
        NotificationChannel.PUSH
      )
    ) {
      promises.push(this.sendPushNotification(notification));
    }

    return Promise.all(promises);
  }

  public auctionFailed(auction: Auction) {
    const playerName = auction.player.firstName + ' ' + auction.player.surname;
    const notification: Notification = {
      email: {
        subject: `Auction ended without bids for ${playerName}`,
        content: `<p>Your player ${playerName} was listed for transfer but received no bids after 3 attempts.</p>
              <p>The player has been removed from the transfer list and remains in your team.</p>`,
        title: 'Transfer Auction Failed',
      },
      push: {
        title: 'Auction Failed',
        body: `Your player ${playerName} was listed for transfer but received no bids after 3 attempts.`,
      },
      inbox: {
        title: 'Auction Failed',
        content: `Your player ${playerName} was listed for transfer but received no bids after 3 attempts. 
        The player has been removed from the transfer list and remains in your team.`,
      },
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public auctionWon(winningTeam: Team, playerName: string) {
    const notification: Notification = {
      email: {
        subject: `You won the auction for ${playerName}!`,
        content: `<p>Congratulations, you were the highest bidder for ${playerName} and the player has now been transferred to ${winningTeam.teamName}.</p>
              <p>The player has expressed their joy at joining ${winningTeam.teamName}, a club they have supported since you agreed to pay them to kick a ball around.</p>`,
        title: 'Fresh Meat',
      },
      push: {
        title: 'Auction Won',
        body: `You were the highest bidder for ${playerName} and the player has now been transferred to ${winningTeam.teamName}.`,
      },
      inbox: {
        title: 'Auction Won',
        content: `You were the highest bidder for ${playerName} and the player has now been transferred to ${winningTeam.teamName}. 
        The player has expressed their joy at joining ${winningTeam.teamName}, a club they have supported since you agreed to pay them to kick a ball around.`,
      },
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public playerSold(winningTeam: Team, playerName: string, receivedFee: number) {
    const notification: Notification = {
      email: {
        subject: `Your player ${playerName} has been sold!`,
        content: `<p>Your player ${playerName} has been sold to ${winningTeam.teamName} for a fee of £${receivedFee}. After agent fees, we will receive ${receivedFee * 0.8}.</p>
              <p>We wish them the best of luck in their new adventure and hope the door doesn't hit them on the way out.</p>`,
        title: 'Kerching!',
      },
      push: {
        title: 'Player Sold',
        body: `Your player ${playerName} has been sold to ${winningTeam.teamName} for a fee of £${receivedFee}.`,
      },
      inbox: {
        title: 'Player Sold',
        content: `Your player ${playerName} has been sold to ${winningTeam.teamName} for a fee of £${receivedFee}. 
        After agent fees, we will receive ${receivedFee * 0.8}.
        We wish them the best of luck in their new adventure and hope the door doesn't hit them on the way out.`,
      },
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public outbidNotification(
    playerName: string,
    newAuctionPrice: number,
    outbiddingTeamName: string
  ) {
    const notification: Notification = {
      email: {
        subject: `You've been outbid on ${playerName}!`,
        content: `<p>Bad news! You've been outbid on ${playerName}.</p>
              <p>The auction price has increased to £${newAuctionPrice.toLocaleString()} thanks to a bid from ${outbiddingTeamName}.</p>
              <p>If you still want this player, you'll need to place a higher bid before the auction ends. Time to dig deeper into those pockets!</p>`,
        title: 'Outbid Alert!',
      },
      push: {
        title: 'Outbid Alert!',
        body: `You've been outbid on ${playerName}. The auction price has increased to £${newAuctionPrice.toLocaleString()}.`,
      },
      inbox: {
        title: 'Outbid Alert!',
        content: `You've been outbid on ${playerName}. The auction price has increased to £${newAuctionPrice.toLocaleString()} thanks to a bid from ${outbiddingTeamName}. 
        If you still want this player, you'll need to place a higher bid before the auction ends. Time to dig deeper into those pockets!`,
      },
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public counterOfferMade(transferRequest: TransferRequest, counterOfferValue: number) {
    const notification: Notification = {
      email: {
        subject: `Counter offer made for ${transferRequest.player.firstName} ${transferRequest.player.surname}`,
        content: `<p>A counter-offer has been made for ${transferRequest.player.firstName} ${transferRequest.player.surname}.</p>
              <p>The counter-offer is for £${counterOfferValue}.</p>`,
        title: 'Counter Offer Made',
      },
      push: {
        title: 'Counter Offer Made',
        body: `A counter-offer has been made for ${transferRequest.player.firstName} ${transferRequest.player.surname} for £${counterOfferValue}.`,
      },
      inbox: {
        title: `Counter Offer Made for ${transferRequest.player.firstName} ${transferRequest.player.surname}`,
        content: `A counter-offer has been made for ${transferRequest.player.firstName} ${transferRequest.player.surname} for £${counterOfferValue}.`,
      },
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public transferRequestWithdrawn(transferRequest: TransferRequest, buyerTeamName: string) {
    const notification: Notification = {
      email: {
        subject: `Transfer request withdrawn for ${transferRequest.player.firstName} ${transferRequest.player.surname}`,
        content: `<p>The transfer request from ${buyerTeamName} for your player ${transferRequest.player.firstName} ${transferRequest.player.surname} has been withdrawn.</p>
              <p>The buying team has decided not to proceed with the transfer at this time.</p>`,
        title: 'Transfer Request Withdrawn',
      },
      push: {
        title: 'Transfer Request Withdrawn',
        body: `The transfer request from ${buyerTeamName} for ${transferRequest.player.firstName} ${transferRequest.player.surname} has been withdrawn.`,
      },
      inbox: {
        title: 'Transfer Request Withdrawn',
        content: `The transfer request from ${buyerTeamName} for ${transferRequest.player.firstName} ${transferRequest.player.surname} has been withdrawn.
        The buying team has decided not to proceed with the transfer at this time.`,
      },
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public sendPlayerRetirementNotification(players: Player[]) {
    if (!players || players.length === 0) {
      logger.warn('No players to send retirement notifications for');
      return;
    }
    if (players.length === 1) {
      const playerName = `${players[0]!.firstName} ${players[0]!.surname}`;
      const age = players[0]!.age;
      const notification: Notification = {
        email: {
          subject: `${players[0]!.surname} announces retirement`,
          content: `<p>Your player ${playerName} (age ${age}) has announced they will be retiring from the beautiful game at the end of the season. Apparently, their knees have filed for divorce and the local pub quiz team is offering a better contract.</p>
<p>They’ll see out the season before swapping shin pads for slippers and a pint. Please join us in raising a glass to their legendary service (and questionable fitness).</p>
<p>We wish them all the best in their new career as a full-time barstool pundit.</p>`,
          title: 'Player Retirement',
        },
        push: {
          title: 'Player Retirement',
          body: `Your player ${playerName} (age ${age}) has announced they will be retiring from the beautiful game at the end of the season.`,
        },
        inbox: {
          title: 'Player Retirement',
          content: `Your player ${playerName} (age ${age}) has announced they will be retiring from the beautiful game at the end of the season. 
          They’ll see out the season before swapping shin pads for slippers and a pint. Please join us in raising a glass to their legendary service (and questionable fitness). 
          We wish them all the best in their new career as a full-time barstool pundit.`,
        },
        category: NotificationCategory.TRANSFERS, // Using TRANSFERS as it's player-related
      };
      return this.sendNotification(notification);
    } else {
      const playerNames = players.map((p) => `${p.firstName} ${p.surname}`).join(', ');
      const ages = players.map((p) => p.age).join(', ');
      const notification: Notification = {
        email: {
          subject: `Players announce retirement`,
          content: `<p>The following legends have decided to retire from pub football: ${playerNames} (ages: ${ages}).</p>
<p>They’ll finish the season before trading muddy boots for comfy slippers and a permanent spot at the bar.</p>
<p>Let’s raise a pint to their years of service, questionable tackles, and even more questionable fitness. May their next season be filled with pub quizzes and fewer pulled hamstrings!</p>`,
          title: 'Player Retirements',
        },
        push: {
          title: 'Player Retirements',
          body: `The following legends have decided to retire from pub football: ${playerNames} (ages: ${ages}).`,
        },
        inbox: {
          title: 'Player Retirements',
          content: `The following legends have decided to retire from pub football: ${playerNames} (ages: ${ages}). 
          They’ll finish the season before trading muddy boots for comfy slippers and a permanent spot at the bar. 
          Let’s raise a pint to their years of service, questionable tackles, and even more questionable fitness. 
          May their next season be filled with pub quizzes and fewer pulled hamstrings!`,
        },
        category: NotificationCategory.TRANSFERS, // Using TRANSFERS as it's player-related
      };
      return this.sendNotification(notification);
    }
  }

  sendTrainingCompleteNotification(playerName: string, attribute: string) {
    const notification: Notification = {
      email: {
        subject: `Training complete for ${playerName}`,
        content: `<p>Your player ${playerName} has reached their full potential for ${attribute} and is off to the pub.</p>
              <p>You should reassign a new player to training or train ${playerName} in a different attribute.</p>`,
        title: 'Training Complete',
      },
      push: {
        title: 'Training Complete',
        body: `Your player ${playerName} has reached their full potential for ${attribute} and is off to the pub.`,
      },
      inbox: {
        title: 'Training Complete',
        content: `Your player ${playerName} has reached their full potential for ${attribute} and is off to the pub. 
        You should reassign a new player to training or train ${playerName} in a different attribute.`,
      },
      category: NotificationCategory.TRAINING,
    };
    return this.sendNotification(notification);
  }

  // Helper to sum all *Potential fields in PlayerAttributes
  private getTotalPotential(attributes: PlayerAttributes): number {
    return (
      attributes.reflexesPotential +
      attributes.positioningPotential +
      attributes.shotStoppingPotential +
      attributes.tacklingPotential +
      attributes.markingPotential +
      attributes.headingPotential +
      attributes.finishingPotential +
      attributes.pacePotential +
      attributes.crossingPotential +
      attributes.passingPotential +
      attributes.visionPotential +
      attributes.ballControlPotential
    );
  }

  // Returns the player with the highest total potential
  private getPlayerWithHighestPotential(players: Player[]): Player | undefined {
    if (!players.length) return undefined;
    return players.reduce(
      (best, player) => {
        if (!player.attributes) return best;
        const playerPotential = this.getTotalPotential(player.attributes as PlayerAttributes);
        const bestPotential =
          best && best.attributes
            ? this.getTotalPotential(best.attributes as PlayerAttributes)
            : -Infinity;
        return playerPotential > bestPotential ? player : best;
      },
      undefined as Player | undefined
    );
  }

  public async sendYouthPlayerNotification(allPlayers: Player[]) {
    const bestPlayer = this.getPlayerWithHighestPotential(allPlayers);
    const notification: Notification = {
      email: {
        subject: `Kids. Who'd have 'em?`,
        content: `<p>Three new yoofs have joined the team. ${allPlayers.map((p) => `${p.firstName} ${p.surname}`).join(', ')}.</p>
              <p>In particular, ${bestPlayer?.firstName} ${bestPlayer?.surname} has shown great promise.</p>`,
        title: 'New Youth Players',
      },
      push: {
        title: 'New Youth Players',
        body: `Three new yoofs have joined your team.`,
      },
      inbox: {
        title: 'New Youth Players',
        content: `Three new yoofs have joined the team. ${allPlayers.map((p) => `${p.firstName} ${p.surname}`).join(', ')}.
        In particular, ${bestPlayer?.firstName} ${bestPlayer?.surname} has shown great promise.`,
      },
      category: NotificationCategory.TRANSFERS,
    };
    return this.sendNotification(notification);
  }

  public async sendMatchReport(matchReport: MatchReport, isHomeTeam: boolean) {
    let title: string;
    let report: string;
    if (matchReport.homeScore > matchReport.awayScore) {
      if (matchReport.homeScore - matchReport.awayScore > 4) {
        title = `${matchReport.homeTeam.teamName} demolish ${matchReport.awayTeam.teamName}`;
        if (isHomeTeam) {
          report = `Our mighty warriors entered the field and left as... mightier warriors! We overcame ${matchReport.awayTeam.teamName} by a margin of ${matchReport.homeScore - matchReport.awayScore} goals.`;
        } else {
          report = `Today was not a good day for our pathetic collective of mostly bipedal "footballers". ${matchReport.homeTeam.teamName} thrashed us by a margin of ${matchReport.homeScore - matchReport.awayScore} goals.`;
        }
      } else if (matchReport.homeScore - matchReport.awayScore > 2) {
        title = `${matchReport.homeTeam.teamName} beat ${matchReport.awayTeam.teamName}`;
        if (isHomeTeam) {
          report = `Our team put in a solid performance and came away with a well-deserved victory against ${matchReport.awayTeam.teamName}.`;
        } else {
          report = `Today was not a good day for our team. ${matchReport.homeTeam.teamName} beat us, but we can take some positives from the game. Steve is off to get a round in at the chippy.`;
        }
      } else {
        title = `${matchReport.homeTeam.teamName} edge past ${matchReport.awayTeam.teamName}`;
        if (isHomeTeam) {
          report = `Our team edged past ${matchReport.awayTeam.teamName} in a closely contested match. We can take some positives from the game, but there is always room for improvement.`;
        } else {
          report = `Today was not a good day for our team. ${matchReport.homeTeam.teamName} edged past us in a closely contested match. We can take some positives from the game, but there is always room for improvement.`;
        }
      }
    } else if (matchReport.homeScore < matchReport.awayScore) {
      if (matchReport.awayScore - matchReport.homeScore > 4) {
        title = `${matchReport.awayTeam.teamName} thrash ${matchReport.homeTeam.teamName}`;
        if (!isHomeTeam) {
          report = `Our mighty warriors entered the field and left as... mightier warriors! We overcame ${matchReport.awayTeam.teamName} by a margin of ${matchReport.awayScore - matchReport.homeScore} goals.`;
        } else {
          report = `Today was not a good day for our pathetic collective of mostly bipedal "footballers". ${matchReport.homeTeam.teamName} thrashed us by a margin of ${matchReport.awayScore - matchReport.homeScore} goals.`;
        }
      } else if (matchReport.awayScore - matchReport.homeScore > 2) {
        title = `${matchReport.awayTeam.teamName} beat ${matchReport.homeTeam.teamName}`;
        if (!isHomeTeam) {
          report = `Our team put in a solid performance and came away with a well-deserved victory against ${matchReport.homeTeam.teamName}.`;
        } else {
          report = `Today was not a good day for our team. ${matchReport.awayTeam.teamName} beat us, but we can take some positives from the game. Steve is off to get a round in at the chippy.`;
        }
      } else {
        title = `${matchReport.awayTeam.teamName} edge past ${matchReport.homeTeam.teamName}`;
        if (!isHomeTeam) {
          report = `Our team edged past ${matchReport.homeTeam.teamName} in a closely contested match. We can take some positives from the game, but there is always room for improvement.`;
        } else {
          report = `Today was not a good day for our team. ${matchReport.awayTeam.teamName} edged past us in a closely contested match. We can take some positives from the game, but there is always room for improvement.`;
        }
      }
    } else {
      title = `${matchReport.homeTeam.teamName} draw with ${matchReport.awayTeam.teamName}`;
      report = `We played out a draw. Both teams gave it everything but in the end, everyone wasted their time and we should have stayed in the pub.`;
    }

    const formatMatchTime = (minute: number, half: number) => {
      const adjustedMinute = half === 2 ? minute + 45 : minute;
      return `${adjustedMinute}'`;
    };

    const notification: Notification = {
      email: {
        subject: `Match Report: ${matchReport.homeTeam.teamName} vs ${matchReport.awayTeam.teamName}`,
        content: JSON.stringify({
          HomeTeamName: matchReport.homeTeam.teamName,
          AwayTeamName: matchReport.awayTeam.teamName,
          HomeScore: matchReport.homeScore,
          AwayScore: matchReport.awayScore,
          HomeScorers: matchReport.homeScorers
            .map(
              (scorer) =>
                `${scorer.goalTime.map((t) => formatMatchTime(t.minute, t.half)).join(', ')} ${scorer.playerName}`
            )
            .join('<br/>'),
          AwayScorers: matchReport.awayScorers
            .map(
              (scorer) =>
                `${scorer.goalTime.map((t) => formatMatchTime(t.minute, t.half)).join(', ')} ${scorer.playerName}`
            )
            .join('<br/>'),
          Report: report,
        }),
        title,
        template: 'matchReport',
      },
      push: {
        title,
        body: `Match Report: ${matchReport.homeTeam.teamName} vs ${matchReport.awayTeam.teamName}`,
      },
      inbox: {
        title,
        content: `Match Report: ${matchReport.homeTeam.teamName} vs ${matchReport.awayTeam.teamName}
        ${report}`,
      },
      category: NotificationCategory.POST_MATCH,
    };
    return this.sendNotification(notification);
  }

  async sendPlayerRetiredNotification(players: Player[]) {
    if (!players || players.length === 0) {
      logger.warn('No players to send retirement notifications for');
      return;
    }
    if (players.length === 1) {
      const playerName = `${players[0]!.firstName} ${players[0]!.surname}`;
      const age = players[0]!.age;
      const notification: Notification = {
        email: {
          subject: `${players[0]!.surname} confirms retirement`,
          content: `<p>Your player ${playerName} (age ${age}) has now hung up his boots and put on his slippers.</p>
<p>We wish them all the best in their new career as a full-time barstool pundit.</p>`,
          title: 'Player Retirement',
        },
        push: {
          title: 'Player Retirement',
          body: `Your player ${playerName} (age ${age}) has now hung up his boots and put on his slippers.`,
        },
        inbox: {
          title: 'Player Retirement',
          content: `Your player ${playerName} (age ${age}) has now hung up his boots and put on his slippers. 
            We wish them all the best in their new career as a full-time barstool pundit.`,
        },
        category: NotificationCategory.TRANSFERS, // Using TRANSFERS as it's player-related
      };
      return this.sendNotification(notification);
    } else {
      const playerNames = players.map((p) => `${p.firstName} ${p.surname}`).join(', ');
      const ages = players.map((p) => p.age).join(', ');
      const notification: Notification = {
        email: {
          subject: `Players confirm retirement`,
          content: `<p>The following legends have now officially retured from pub football: ${playerNames} (ages: ${ages}).</p>
<p>Let’s raise a pint to their years of service, questionable tackles, and even more questionable fitness.</p>`,
          title: 'Player Retirements',
        },
        push: {
          title: 'Player Retirements',
          body: `The following legends have now officially retired from pub football: ${playerNames} (ages: ${ages}).`,
        },
        inbox: {
          title: 'Player Retirements',
          content: `The following legends have now officially retired from pub football: ${playerNames} (ages: ${ages}). 
          Let’s raise a pint to their years of service, questionable tackles, and even more questionable fitness.`,
        },
        category: NotificationCategory.TRANSFERS, // Using TRANSFERS as it's player-related
      };
      return this.sendNotification(notification);
    }
  }

  async sendScoutingCompleteNotification() {
    const notification: Notification = {
      email: {
        subject: `Scouting complete`,
        content: `<p>Your scouts have returned with their reports. Log in to the app to view the random assortment of layabouts they have found.</p>`,
        title: 'Scouting Complete',
      },
      push: {
        title: 'Scouting Complete',
        body: `Your scouts have returned with their reports`,
      },
      inbox: {
        title: 'Scouting Complete',
        content: `Your scouts have returned with their reports. Go to the transfers section to view the random assortment of layabouts they have found.`,
      },
      category: NotificationCategory.SCOUTING_RESULTS,
    };
    return this.sendNotification(notification);
  }
}
