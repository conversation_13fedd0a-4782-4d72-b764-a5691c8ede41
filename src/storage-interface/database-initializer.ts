import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { logger } from '@/utils/logger.js';
import { MikroORM } from '@mikro-orm/core';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';

import { ManagerRepository } from '@/storage-interface/managers/manager-repository.interface.js';
import { MikroOrmManagerRepository } from '@/storage-interface/managers/mikro-orm-manager-repository.js';
import { PlayerRepository } from '@/storage-interface/players/index.js';
import { MikroOrmPlayerRepository } from '@/storage-interface/players/mikro-orm-player-repository.js';
import { PurchaseRepository } from '@/storage-interface/purchases/index.js';
import { MikroOrmPurchaseRepository } from '@/storage-interface/purchases/mikro-orm-purchase-repository.js';
import { ScoutingRequestRepository } from '@/storage-interface/scouting-requests/index.js';
import { MikroOrmScoutingRequestRepository } from '@/storage-interface/scouting-requests/mikro-orm-scouting-request-repository.js';
import { MikroOrmTeamTrainingSlotRepository } from '@/storage-interface/training/mikro-orm-training-repository.js';
import { TeamTrainingSlotRepository } from '@/storage-interface/training/training-repository.interface.js';
import mikroOrmConfig from '../../mikro-orm.config.js';
import { IFixtureRepository } from './fixtures/fixture-repository.interface.js';
import { FixtureRepository } from './fixtures/fixture-repository.js';
import { GameworldRepository } from './gameworld/gameworld-repository.interface.js';
import { MikroOrmGameworldRepository } from './gameworld/mikro-orm-gameworld-repository.js';
import { DynamoInboxRepository } from './inbox/dynamo-inbox-repository.js';
import { InboxRepository } from './inbox/inbox-repository.interface.js';
import { LeagueRepository } from './leagues/league-repository.interface.js';
import { MikroOrmLeagueRepository } from './leagues/mikro-orm-league-repository.js';
import { MikroOrmService } from './mikro-orm-service.js';
import { MikroOrmScoutingRepository } from './scouting/mikro-orm-scouting-repository.js';
import { ScoutingRepository } from './scouting/scouting-repository.interface.js';
import { MikroOrmTeamRepository } from './teams/mikro-orm-team-repository.js';
import { TeamRepository } from './teams/team-repository.interface.js';
import { MikroOrmTransferRepository } from './transfers/mikro-orm-transfer-repository.js';
import { TransferRepository } from './transfers/transfer-repository.interface.js';

// Singleton instance of MikroOrmService
let mikroOrmServiceInstance: MikroOrmService | null = null;
let isInitializing = false;
let initializationPromise: Promise<void> | null = null;

// Repository cache for lazy loading
const repositoryCache = new Map<string, any>();

/**
 * Initializes the database connection if it hasn't been initialized yet
 * @returns A promise that resolves when the database is initialized
 */
export async function initializeDatabase(): Promise<void> {
  // If already initialized, return immediately
  if (mikroOrmServiceInstance?.isInitialized()) {
    return Promise.resolve();
  }

  // If initialization is in progress, return the existing promise
  if (isInitializing && initializationPromise) {
    return initializationPromise;
  }

  // Start initialization
  isInitializing = true;
  initializationPromise = new Promise<void>((resolve, reject) => {
    logger.info('Initializing MikroORM database connection');
    MikroORM.init<PostgreSqlDriver>(mikroOrmConfig)
      .then((orm) => {
        mikroOrmServiceInstance = new MikroOrmService(Promise.resolve(orm));
        return mikroOrmServiceInstance.initialize();
      })
      .then(() => {
        logger.info('MikroORM database connection initialized successfully');
        isInitializing = false;
        resolve();
      })
      .catch((error) => {
        isInitializing = false;
        logger.error('Failed to initialize database', { error });
        reject(error);
      });
  });

  return initializationPromise;
}

/**
 * Gets the MikroOrmService instance, initializing it if necessary
 * @returns The MikroOrmService instance
 */
export async function getMikroOrmService(): Promise<MikroOrmService> {
  await initializeDatabase();

  if (!mikroOrmServiceInstance) {
    const orm = await MikroORM.init<PostgreSqlDriver>(mikroOrmConfig);
    mikroOrmServiceInstance = new MikroOrmService(Promise.resolve(orm));
    await mikroOrmServiceInstance.initialize();
  }

  return mikroOrmServiceInstance;
}

/**
 * Gets a LeagueRepository instance with caching
 * @returns A promise that resolves to a LeagueRepository
 */
export async function getLeagueRepository(): Promise<LeagueRepository> {
  const cacheKey = 'LeagueRepository';
  if (repositoryCache.has(cacheKey)) {
    return repositoryCache.get(cacheKey);
  }

  const mikroOrmService = await getMikroOrmService();
  const repository = new MikroOrmLeagueRepository(mikroOrmService);
  repositoryCache.set(cacheKey, repository);
  return repository;
}

/**
 * Gets a TeamRepository instance
 * @returns A promise that resolves to a TeamRepository
 */
export async function getTeamRepository(): Promise<TeamRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmTeamRepository(mikroOrmService);
}

/**
 * Gets a FixtureRepository instance
 * @returns A promise that resolves to a FixtureRepository
 */
export async function getFixtureRepository(): Promise<IFixtureRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new FixtureRepository(mikroOrmService);
}

/**
 * Gets a PlayerRepository instance
 * @returns A promise that resolves to a PlayerRepository
 */
export async function getPlayerRepository(): Promise<PlayerRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmPlayerRepository(mikroOrmService);
}

/**
 * Gets a ManagerRepositoryInterface instance
 * @returns A promise that resolves to a ManagerRepositoryInterface
 */
export async function getManagerRepository(): Promise<ManagerRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmManagerRepository(mikroOrmService);
}

/**
 * Gets a PurchaseRepository instance
 * @returns A promise that resolves to a PurchaseRepository
 */
export async function getPurchaseRepository(): Promise<PurchaseRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmPurchaseRepository(mikroOrmService);
}

/**
 * Gets a ScoutingRepository instance
 * @returns A promise that resolves to a ScoutingRepository
 */
export async function getScoutingRepository(): Promise<ScoutingRepository> {
  const playerRepository = await getPlayerRepository();
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmScoutingRepository(mikroOrmService, playerRepository);
}

/**
 * Gets a ScoutingRequestRepository instance
 * @returns A promise that resolves to a ScoutingRequestRepository
 */
export async function getScoutingRequestRepository(): Promise<ScoutingRequestRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmScoutingRequestRepository(mikroOrmService);
}

/**
 * Gets a GameworldRepository instance
 * @returns A promise that resolves to a GameworldRepository
 */
export async function getGameworldRepository(): Promise<GameworldRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmGameworldRepository(mikroOrmService);
}

/**
 * Gets a GameworldRepository instance
 * @returns A promise that resolves to a GameworldRepository
 */
export async function getTrainingRepository(): Promise<TeamTrainingSlotRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmTeamTrainingSlotRepository(mikroOrmService);
}

/**
 * Gets a TransferRepository instance
 * @returns A promise that resolves to a TransferRepository
 */
export async function getTransferRepository(): Promise<TransferRepository> {
  const mikroOrmService = await getMikroOrmService();
  return new MikroOrmTransferRepository(mikroOrmService);
}

/**
 * Gets an InboxRepository instance
 * Always returns DynamoDB implementation regardless of DATABASE_TYPE
 * @returns A promise that resolves to an InboxRepository
 */
export async function getInboxRepository(): Promise<InboxRepository> {
  const dynamoDb = new DynamoDbService();
  return new DynamoInboxRepository(dynamoDb);
}
