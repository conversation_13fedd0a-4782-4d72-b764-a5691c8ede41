import { Fixture } from '@/entities/Fixture.js';
import { FixtureDetails, MatchEvent, MatchStats } from '@/model/fixture.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { GamePlayer } from '@/simulation/types.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import { SimulateFixturesEvent } from '@/types/generated/index.js';
import { logger } from '@/utils/logger.js';
import { FilterQuery } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';
import { GetFixturesByTeamResponse, IFixtureRepository } from './fixture-repository.interface.js';

export class FixtureRepository implements IFixtureRepository {
  private dynamoDb: DynamoDbService;

  constructor(private mikroOrmService: MikroOrmService) {
    this.dynamoDb = new DynamoDbService();
  }

  getEntityManager(): SqlEntityManager {
    return this.mikroOrmService.getEntityManager();
  }
  createFromPK(id: string): Fixture {
    return this.mikroOrmService.getEntityManager().getReference(Fixture, id);
  }

  async batchInsertFixtures(fixtures: Fixture[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const fixture of fixtures) {
        em.persist(fixture);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create fixtures:', { error });
      throw error;
    }
  }

  async getFixture(fixtureId: string): Promise<FixtureDetails | null> {
    return this.dynamoDb.get<FixtureDetails>(process.env.FIXTURE_DETAIL_TABLE_NAME!, {
      fixtureId,
    });
  }

  async getFixturesByLeague(gameworldId: string, leagueId: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Fixture> = { gameworldId, league: { id: leagueId } };
    return em.find(Fixture, where);
  }

  async getFixturesByTeam(
    gameworldId: string,
    teamId: string
  ): Promise<GetFixturesByTeamResponse[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Fixture> = {
      gameworldId,
      $or: [{ homeTeam: { teamId } }, { awayTeam: { teamId } }],
    };
    const fixtures = await em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
      // Only load essential team fields, not all the player data
      fields: [
        'fixtureId',
        'gameworldId',
        'date',
        'played',
        'score',
        'scorers',
        'simulatedAt',
        'seed',
        'homeTeam.teamId',
        'homeTeam.teamName',
        'awayTeam.teamId',
        'awayTeam.teamName',
      ],
    });

    return fixtures;
  }

  async getDueFixtures(
    gameworldId?: string,
    leagueId?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<SimulateFixturesEvent[]> {
    const em = this.mikroOrmService.getEntityManager();
    const currentTime = Date.now() + 5 * 60 * 1000; // 5 minutes in the future to make sure we get all the fixtures

    const where: FilterQuery<Fixture> = {
      date: { $lte: currentTime },
      played: false,
    };

    if (gameworldId) {
      where.gameworldId = gameworldId;
    }

    if (leagueId) {
      where.league = { id: leagueId };
    }

    // Only select the required fields and join homeTeam/awayTeam for their IDs
    const fixtures = await em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      limit,
      offset,
      populate: ['homeTeam', 'awayTeam'],
      fields: ['gameworldId', 'league', 'fixtureId', 'date', 'homeTeam.teamId', 'awayTeam.teamId'],
    });

    // Map to the required shape
    return fixtures.map((f) => ({
      gameworldId: f.gameworldId,
      leagueId: f.league.id,
      fixtureId: f.fixtureId,
      homeTeamId: f.homeTeam.teamId,
      awayTeamId: f.awayTeam.teamId,
      fixtureDate: f.date,
    }));
  }

  async getAllUnplayedFixtures(gameworldId?: string, leagueId?: string): Promise<Fixture[]> {
    const em = this.mikroOrmService.getEntityManager();

    const where: FilterQuery<Fixture> = {
      played: false,
    };

    if (gameworldId) {
      where.gameworldId = gameworldId;
    }

    if (leagueId) {
      where.league = { id: leagueId };
    }

    return em.find(Fixture, where, {
      orderBy: { date: 'ASC' },
      populate: ['homeTeam', 'awayTeam'],
    });
  }

  async updateFixtureResult(
    gameworldId: string,
    leagueId: string,
    fixtureId: string,
    seed: number,
    stats: MatchStats,
    events: MatchEvent[],
    homePlayers: GamePlayer[],
    awayPlayers: GamePlayer[],
    hasHumanManager: boolean
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const fixture = await em.findOne(Fixture, {
      gameworldId,
      league: { id: leagueId },
      fixtureId,
    });

    if (!fixture) {
      throw new Error(`Fixture not found: ${gameworldId}, ${leagueId}, ${fixtureId}`);
    }

    fixture.score = stats.score;
    fixture.scorers = stats.scorers;
    fixture.played = true;
    fixture.seed = seed;
    fixture.simulatedAt = Date.now();

    await em.persistAndFlush(fixture);

    // This is surprisingly expensive to do because of the large amount of data we're writing.
    // If both teams are AI then we don't need to store the details as nobody will ever see them
    if (hasHumanManager) {
      await this.dynamoDb.insert<FixtureDetails>(process.env.FIXTURE_DETAIL_TABLE_NAME!, {
        fixtureId,
        gameworldId,
        leagueId,
        homeTeamId: fixture.homeTeam.teamId,
        homeTeamName: fixture.homeTeam.teamName,
        awayTeamId: fixture.awayTeam.teamId,
        awayTeamName: fixture.awayTeam.teamName,
        date: fixture.date,
        stats,
        events,
        homePlayers: homePlayers.map((p) => ({
          playerId: p.player.playerId,
          playerName: p.player.firstName + ' ' + p.player.surname,
          joinedMatchMinute: p.joinedMatchMinute,
          leftMatchMinute: p.leftMatchMinute,
          rating: p.rating,
          sentOff: p.sentOff,
          injured: p.injured,
          goals: p.goals,
          assists: p.assists,
          substitute: p.substitute?.player.playerId,
        })),
        awayPlayers: awayPlayers.map((p) => ({
          playerId: p.player.playerId,
          playerName: p.player.firstName + ' ' + p.player.surname,
          joinedMatchMinute: p.joinedMatchMinute,
          leftMatchMinute: p.leftMatchMinute,
          rating: p.rating,
          sentOff: p.sentOff,
          injured: p.injured,
          goals: p.goals,
          assists: p.assists,
          substitute: p.substitute?.player.playerId,
        })),
      });
    }
  }
}
