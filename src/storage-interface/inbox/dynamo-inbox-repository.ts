import { Inbox } from '@/entities/Inbox.js';
import DynamoDbService from '@/services/database/dynamo/dynamo-db-service.js';
import { logger } from '@/utils/logger.js';
import { v4 as uuidv4 } from 'uuid';
import { InboxRepository } from './inbox-repository.interface.js';

/**
 * DynamoDB implementation of the InboxRepository
 */
export class DynamoInboxRepository implements InboxRepository {
  constructor(private readonly dynamoDb: DynamoDbService) {}

  /**
   * Create a new inbox message
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param date The date when the message was sent
   * @param message The message content
   * @param extra Additional information
   * @returns The created inbox message
   */
  async createMessage(
    gameworldId: string,
    teamId: string,
    date: number,
    message: string,
    extra: string
  ): Promise<Inbox> {
    try {
      const id = uuidv4();
      const inboxMessage: Inbox = {
        id,
        gameworldId,
        teamId,
        date,
        message,
        extra,
      };

      // Create composite key for DynamoDB
      const dbItem = {
        gameworldId_teamId: `${gameworldId}#${teamId}`,
        id,
        gameworldId,
        teamId,
        date,
        message,
        extra,
      };

      await this.dynamoDb.insert(process.env.INBOX_TABLE_NAME!, dbItem);

      return inboxMessage;
    } catch (error) {
      logger.error('Failed to create inbox message:', { error, gameworldId, teamId, message });
      throw error;
    }
  }

  /**
   * Get an inbox message by ID
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param messageId The ID of the message
   * @returns The inbox message, or null if not found
   */
  async getMessage(gameworldId: string, teamId: string, messageId: string): Promise<Inbox | null> {
    try {
      const result = await this.dynamoDb.get(process.env.INBOX_TABLE_NAME!, {
        gameworldId_teamId: `${gameworldId}#${teamId}`,
        id: messageId,
      });

      if (!result) {
        return null;
      }

      return this.mapDbItemToInbox(result);
    } catch (error) {
      logger.error('Failed to get inbox message:', { error, gameworldId, teamId, messageId });
      throw error;
    }
  }

  /**
   * Get all inbox messages for a specific gameworld and team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit Optional limit for pagination
   * @returns Array of inbox messages
   */
  async getMessagesByGameworldAndTeam(
    gameworldId: string,
    teamId: string,
    limit?: number
  ): Promise<Inbox[]> {
    try {
      const result = await this.dynamoDb.query<Inbox>(
        process.env.INBOX_TABLE_NAME!,
        {
          hashKey: {
            name: 'gameworldId_teamId',
            value: `${gameworldId}#${teamId}`,
          },
        },
        undefined, // no index
        {
          limit,
        }
      );

      if (!result.items || result.items.length === 0) {
        return [];
      }

      // Sort by date descending (most recent first)
      const sortedItems = result.items.sort((a, b) => b.date - a.date);

      return sortedItems.map((item) => this.mapDbItemToInbox(item));
    } catch (error) {
      logger.error('Failed to get inbox messages by gameworld and team:', {
        error,
        gameworldId,
        teamId,
      });
      throw error;
    }
  }

  /**
   * Update an inbox message
   * @param message The inbox message to update
   * @returns The updated inbox message
   */
  async updateMessage(message: Inbox): Promise<Inbox> {
    try {
      const updateData = {
        date: message.date,
        message: message.message,
        extra: message.extra,
      };

      await this.dynamoDb.update(
        process.env.INBOX_TABLE_NAME!,
        {
          gameworldId_teamId: `${message.gameworldId}#${message.teamId}`,
          id: message.id,
        },
        updateData
      );

      return message;
    } catch (error) {
      logger.error('Failed to update inbox message:', { error, id: message.id });
      throw error;
    }
  }

  /**
   * Delete an inbox message
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param messageId The ID of the message to delete
   */
  async deleteMessage(gameworldId: string, teamId: string, messageId: string): Promise<void> {
    try {
      await this.dynamoDb.delete(process.env.INBOX_TABLE_NAME!, {
        gameworldId_teamId: `${gameworldId}#${teamId}`,
        id: messageId,
      });
    } catch (error) {
      logger.error('Failed to delete inbox message:', { error, gameworldId, teamId, messageId });
      throw error;
    }
  }

  /**
   * Map DynamoDB item to Inbox entity
   * @param item DynamoDB item
   * @returns Inbox entity
   */
  private mapDbItemToInbox(item: any): Inbox {
    return {
      id: item.id,
      gameworldId: item.gameworldId,
      teamId: item.teamId,
      date: item.date,
      message: item.message,
      extra: item.extra,
    };
  }
}
