import { Player } from '@/entities/Player.js';
import { PlayerMatchHistory } from '@/entities/PlayerMatchHistory.js';
import { ScoutedPlayer } from '@/entities/ScoutedPlayer.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import {
  AttributeUpdate,
  PlayerRepository,
} from '@/storage-interface/players/player-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Loaded, raw, Rel } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';

export class MikroOrmPlayerRepository implements PlayerRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  getEntityManager(): SqlEntityManager {
    return this.mikroOrmService.getEntityManager();
  }
  createFromPK(playerId: string): Rel<Player> {
    const em = this.mikroOrmService.getEntityManager();
    return em.getReference(Player, playerId);
  }

  async batchCreateTransferListedPlayers(
    transferListedPlayers: TransferListedPlayer[]
  ): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const player of transferListedPlayers) {
        em.persist(player.player);
        em.persist(player);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create leagues:', { error });
      throw error;
    }
  }

  async batchCreatePlayers(players: Player[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const player of players) {
        em.persist(player);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create leagues:', { error });
      throw error;
    }
  }
  async getPlayer(gameworldId: string, playerId: string): Promise<Player | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(
        Player,
        { gameworldId, playerId },
        {
          populate: ['team', 'attributes'],
        }
      );
    } catch (error) {
      logger.error('Failed to get player:', { error, gameworldId, playerId });
      throw error;
    }
  }
  getPlayersByTeam(gameworldId: string, teamId: string): Promise<Player[]> {
    throw new Error('Method not implemented.');
  }
  getPlayersByLeague(gameworldId: string, leagueId: string): Promise<Player[]> {
    throw new Error('Method not implemented.');
  }
  getPlayersWithoutTeam(gameworldId: string): Promise<Player[]> {
    throw new Error('Method not implemented.');
  }

  async getPlayersByGameworld(gameworldId: string): Promise<Player[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(Player, { gameworldId }, { populate: ['team', 'attributes'] });
    } catch (error) {
      logger.error('Error getting players by gameworld', { gameworldId, error });
      throw error;
    }
  }

  async updatePlayer(player: Player): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      em.persist(player);
      await em.flush();
    } catch (error) {
      logger.error('Error updating player', { player, error });
      throw error;
    }
  }
  updatePlayerStats(updatedPlayers: Player[]): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    for (const update of updatedPlayers) {
      em.persist(update);
    }
    return em.flush();
  }

  async assignPlayerToTeam(gameworldId: string, playerId: string, teamId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();

    // Directly update the player's teamId using nativeUpdate
    await em.nativeUpdate(Player, { gameworldId, playerId }, { team: teamId });
  }

  async removePlayerFromTeam(gameworldId: string, playerId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();

    // Directly update the player's team to null using nativeUpdate
    await em.nativeUpdate(Player, { gameworldId, playerId }, { team: null });
  }

  async removePlayer(gameworldId: string, playerId: string): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      await em.nativeDelete(Player, { gameworldId, playerId });
    } catch (error) {
      logger.error('Error removing player', { gameworldId, playerId, error });
      throw error;
    }
  }
  addPlayerMatchHistory(
    gameworldId: string,
    playerId: string,
    fixtureId: string,
    stats: PlayerMatchHistory
  ): Promise<void> {
    throw new Error('Method not implemented.');
  }
  getPlayerMatchHistory(
    gameworldId: string,
    playerId: string
  ): Promise<Array<{ fixtureId: string; stats: PlayerMatchHistory }>> {
    throw new Error('Method not implemented.');
  }

  /**
   * Check if a player has been scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param playerId The player ID
   * @param teamId The team ID
   * @returns True if the player has been scouted by the team, false otherwise
   */
  async isPlayerScoutedByTeam(
    gameworldId: string,
    playerId: string,
    teamId: string
  ): Promise<boolean> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find the player and team entities first
      const player = await em.findOne(Player, { gameworldId, playerId });
      if (!player) {
        logger.warn('Player not found when checking if scouted', { gameworldId, playerId, teamId });
        return false;
      }

      // Check if there's a scouted player record
      const scoutedPlayer = await em.findOne(ScoutedPlayer, {
        gameworldId,
        team: teamId,
        player: playerId,
      });

      return !!scoutedPlayer;
    } catch (error) {
      logger.error('Error checking if player is scouted by team', {
        gameworldId,
        playerId,
        teamId,
        error,
      });
      throw error;
    }
  }

  /**
   * Get all players scouted by a specific team
   * @param gameworldId The gameworld ID
   * @param teamId The team ID
   * @param limit The number of players to return
   * @param lastEvaluatedKey The last evaluated key
   * @returns Array of scouted players
   */
  async getPlayersScoutedByTeam(
    gameworldId: string,
    teamId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<Loaded<ScoutedPlayer, 'player' | 'player.attributes'>[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find all scouted players for the team, excluding players who are already in the team
      return await em.find(
        ScoutedPlayer,
        {
          gameworldId,
          team: teamId,
          player: {
            team: { $ne: teamId },
          },
        },
        {
          populate: ['player', 'player.attributes', 'player.team.league'],
          orderBy: { scoutedAt: 'DESC' },
          limit: limit,
          offset: lastEvaluatedKey ? Number.parseInt(lastEvaluatedKey) : undefined,
        }
      );
    } catch (error) {
      logger.error('Error getting players scouted by team', { gameworldId, teamId, error });
      throw error;
    }
  }

  /**
   * Get random players from a league
   * @param gameworldId The gameworld ID
   * @param leagueId The league ID
   * @param count The number of random players to return
   * @returns Array of random players from the league
   */
  async getRandomPlayersFromLeague(
    gameworldId: string,
    leagueId: string,
    count: number
  ): Promise<Player[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Use PostgreSQL's RANDOM() function to get random players
      // This is much more efficient than fetching all players and shuffling in memory
      return await em.find(
        Player,
        { gameworldId },
        {
          orderBy: { [raw('RANDOM()')]: 'ASC' },
          limit: count,
          populate: ['team', 'attributes'],
          filters: { team: { league: { id: leagueId } } },
        }
      );
    } catch (error) {
      logger.error('Error getting random players from league', {
        gameworldId,
        leagueId,
        count,
        error,
      });
      throw error;
    }
  }

  async updatePlayerAttributesBatch(updates: AttributeUpdate[]): Promise<void> {
    if (updates.length === 0) return;
    const em = this.mikroOrmService.getEntityManager();

    // Group updates by attribute for separate queries per attribute
    const updatesByAttribute: Record<string, AttributeUpdate[]> = {};
    for (const update of updates) {
      if (!updatesByAttribute[update.attribute]) {
        updatesByAttribute[update.attribute] = [];
      }
      updatesByAttribute[update.attribute]!.push(update);
    }

    for (const [attribute, attrUpdates] of Object.entries(updatesByAttribute)) {
      let column;
      if (attribute === 'stamina') {
        column = 'stamina';
      } else if (attribute.endsWith('Current')) {
        column = `${attribute.slice(0, -7)}_current`;
      } else {
        column = `${attribute}_current`;
      }
      const cases = attrUpdates
        .map((u) => `WHEN player_id = '${u.playerId}' THEN ${column} + ${u.attributeIncrement}`)
        .join(' ');
      const ids = attrUpdates.map((u) => `'${u.playerId}'`).join(', ');

      const sql = `
      UPDATE player_attributes
      SET ${column} = CASE ${cases} ELSE ${column} END
      WHERE player_id IN (${ids})
    `;
      await em.getConnection().execute(sql);
    }
  }

  async getPlayerWithStats(gameworldId: string, playerId: string): Promise<Player | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(
        Player,
        { gameworldId, playerId },
        {
          populate: ['overallStats', 'matchHistory.fixture.homeTeam', 'matchHistory.fixture.awayTeam'],
        }
      );
    } catch (error) {
      logger.error('Failed to get player with stats:', { error, gameworldId, playerId });
      throw error;
    }
  }
}
