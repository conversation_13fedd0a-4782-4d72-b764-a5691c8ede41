import { AvailableTeam } from '@/entities/AvailableTeam.js';
import { Fixture } from '@/entities/Fixture.js';
import { League } from '@/entities/League.js';
import { Team } from '@/entities/Team.js';
import { Transactions } from '@/entities/Transactions.js';
import { STARTING_BALANCE } from '@/functions/generate/constants.js';
import { TeamMovement } from '@/functions/league/logic/LeagueProcessorV2.js';
import { LeagueStandings } from '@/model/team.js';
import { SQS } from '@/services/sqs/sqs.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import {
  TeamRepository,
  TransactionType,
} from '@/storage-interface/teams/team-repository.interface.js';
import { TransferRepository } from '@/storage-interface/transfers/index.js';
import { logger } from '@/utils/logger.js';
import { tracer } from '@/utils/tracer.js';
import { FilterQuery, raw, type Rel } from '@mikro-orm/core';
import { SqlEntityManager } from '@mikro-orm/postgresql';
import { v4 as uuidv4 } from 'uuid';
import { PlayerRepository } from '../players/player-repository.interface.ts';

export class MikroOrmTeamRepository implements TeamRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  getEntityManager(): SqlEntityManager {
    return this.mikroOrmService.getEntityManager();
  }

  createFromPK(teamId: string): Rel<Team> {
    const em = this.mikroOrmService.getEntityManager();
    return em.getReference(Team, teamId);
  }

  async batchInsertTeams(teams: Team[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const team of teams) {
        em.persist(team);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create teams:', { error });
      throw error;
    }
  }

  async batchInsertAvailableTeams(teams: AvailableTeam[]): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      for (const team of teams) {
        em.persist(team);
      }
      await em.flush();
    } catch (error) {
      logger.error('Failed to create available teams:', { error });
      throw error;
    }
  }

  async getTeamsByGameworld(gameworldId: string, includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { gameworldId };
    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes', 'manager'],
            exclude: ['players.team'],
          }
        : {
            populate: ['manager'],
          }
    );
  }

  async getTeamsByLeague(leagueId: string, includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { league: leagueId };
    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes', 'manager'],
            exclude: ['players.team'],
          }
        : {
            populate: ['manager'],
          }
    );
  }

  async updateTeamStandings(
    teamId: string,
    gameworldId: string,
    standings: LeagueStandings,
    flush = true
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const team = await em.findOne(Team, { teamId, gameworldId });
    if (!team) {
      throw new Error(`Team not found: ${teamId}`);
    }

    team.points = standings.points;
    team.goalsFor = standings.goalsFor;
    team.goalsAgainst = standings.goalsAgainst;
    team.wins = standings.wins;
    team.draws = standings.draws;
    team.losses = standings.losses;
    team.played = standings.played;

    if (flush) {
      return em.persistAndFlush(team);
    } else {
      em.persist(team);
    }
  }

  async updateTeamLeague(teamId: string, gameworldId: string, newLeagueId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { teamId, gameworldId };
    const team = await em.findOne(Team, where);

    if (team) {
      team.league = em.getReference(League, newLeagueId);
      team.points = 0;
      team.goalsFor = 0;
      team.goalsAgainst = 0;
      team.wins = 0;
      team.draws = 0;
      team.losses = 0;
      team.played = 0;

      await em.persistAndFlush(team);
    }
  }

  async resetTeamStandings(teamId: string, gameworldId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { teamId, gameworldId };
    const team = await em.findOne(Team, where);

    if (team) {
      team.points = 0;
      team.goalsFor = 0;
      team.goalsAgainst = 0;
      team.wins = 0;
      team.draws = 0;
      team.losses = 0;
      team.played = 0;

      await em.persistAndFlush(team);
    }
  }

  async updateTeamLeagues(
    teams: Team[],
    movements: TeamMovement[],
    gameworldId: string
  ): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Start a transaction
      await em.transactional(async (txEm) => {
        // Process team movements
        for (const movement of movements) {
          const where: FilterQuery<Team> = { teamId: movement.teamId, gameworldId };
          const team = await txEm.findOne(Team, where);

          if (team) {
            team.league = em.getReference(League, movement.toLeagueId);
            team.points = 0;
            team.goalsFor = 0;
            team.goalsAgainst = 0;
            team.wins = 0;
            team.draws = 0;
            team.losses = 0;
            team.played = 0;

            txEm.persist(team);
          }
        }

        // Reset standings for stationary teams
        const stationaryTeams = teams.filter(
          (team) => !movements.some((movement) => movement.teamId === team.teamId)
        );

        for (const team of stationaryTeams) {
          const where: FilterQuery<Team> = { teamId: team.teamId, gameworldId };
          const dbTeam = await txEm.findOne(Team, where);

          if (dbTeam) {
            dbTeam.points = 0;
            dbTeam.goalsFor = 0;
            dbTeam.goalsAgainst = 0;
            dbTeam.wins = 0;
            dbTeam.draws = 0;
            dbTeam.losses = 0;
            dbTeam.played = 0;

            txEm.persist(dbTeam);
          }
        }
      });

      logger.info('Successfully updated team leagues and reset standings', {
        totalTeams: teams.length,
        movedTeams: movements.length,
        stationaryTeams: teams.length - movements.length,
      });
    } catch (error) {
      logger.error('Failed to update team leagues', { error });
      throw error;
    }
  }

  async countAvailableTeams(): Promise<number> {
    const em = this.mikroOrmService.getEntityManager();
    return em.count(AvailableTeam);
  }

  async getRandomAvailableTeam(): Promise<AvailableTeam | null> {
    const em = this.mikroOrmService.getEntityManager();
    const qb = em.qb(AvailableTeam, 'at');

    const [availableTeam] = await qb
      .orderBy({ [raw(`RANDOM()`)]: 'ASC' })
      .limit(1)
      .getResult();

    return availableTeam || null;
  }

  async deleteAvailableTeam(team: AvailableTeam): Promise<number> {
    const em = this.mikroOrmService.getEntityManager();
    return em.nativeDelete(AvailableTeam, { id: team.id });
  }

  async getTeam(
    gameworldId: string,
    teamId: string,
    includePlayers: boolean
  ): Promise<Team | null> {
    const em = this.mikroOrmService.getEntityManager();
    return await em.findOne(
      Team,
      { teamId, gameworldId },
      includePlayers
        ? {
            populate: [
              'players',
              'players.attributes',
              'players.matchHistory', // needed to calculate average rating
              'manager',
            ],
            exclude: ['players.team'],
          }
        : undefined
    );
  }

  /**
   * Get team with minimal player data for simulation (no match history)
   */
  async getTeamForSimulation(gameworldId: string, teamId: string): Promise<Team | null> {
    const em = this.mikroOrmService.getEntityManager();
    return await em.findOne(
      Team,
      { teamId, gameworldId },
      {
        populate: ['players', 'players.attributes', 'manager'],
        exclude: ['players.team'],
      }
    );
  }

  async getTeamAndNextMatch(
    gameworldId: string,
    teamId: string,
    includePlayers: boolean
  ): Promise<{ team: Team | null; nextFixture: Fixture | null }> {
    const em = this.mikroOrmService.getEntityManager();

    // Get the team with optional player details (optimized - no match history or overall stats)
    const team = await em.findOne(
      Team,
      { teamId, gameworldId },
      includePlayers
        ? {
            populate: ['players', 'players.attributes', 'players.matchHistory'], // get match history to calculate average rating
            exclude: ['players.team', 'manager'],
          }
        : {
            populate: [],
            exclude: ['manager'],
          }
    );

    // Find the next unplayed fixture for this team
    const nextFixture = await em.findOne(
      Fixture,
      {
        gameworldId,
        played: false,
        $or: [{ homeTeam: { teamId } }, { awayTeam: { teamId } }],
      },
      {
        orderBy: { date: 'ASC' },
      }
    );

    return { team, nextFixture };
  }

  /**
   * Update a team's balance
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param amount The amount to add to the current balance
   * @param type The type of transaction
   * @param flush Should we flush the database
   */
  async updateTeamBalance(
    teamId: string,
    gameworldId: string,
    amount: number,
    type: TransactionType,
    flush: boolean = true
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();

    if (amount === 0) {
      return;
    }

    const result = await em.nativeUpdate(
      Team,
      { teamId, gameworldId },
      { balance: raw(`balance + ${amount}`) }
    );
    if (result === 0) {
      throw new Error(`Team not found: ${teamId}`);
    }

    em.persist(
      new Transactions({
        gameworldId,
        team: em.getReference(Team, teamId),
        date: Date.now(),
        amount,
        type,
        details: JSON.stringify({}),
      })
    );

    if (flush) {
      await em.flush();
    }
  }

  /**
   * Update a team's balance
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param items Array of individual transactions
   * @param flush Should we flush the database
   */
  async updateTeamBalances(
    teamId: string,
    gameworldId: string,
    items: { amount: number; type: TransactionType }[],
    flush: boolean = true
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();

    // sum all amounts
    const amount = items.reduce((acc, item) => acc + item.amount, 0);
    if (amount === 0) {
      return;
    }

    const result = await em.nativeUpdate(
      Team,
      { teamId, gameworldId },
      { balance: raw(`balance + ${amount}`) }
    );
    if (result === 0) {
      throw new Error(`Team not found: ${teamId}`);
    }

    for (const item of items) {
      em.persist(
        new Transactions({
          gameworldId,
          team: em.getReference(Team, teamId),
          date: Date.now(),
          amount: item.amount,
          type: item.type,
          details: JSON.stringify({}),
        })
      );
    }

    if (flush) {
      await em.flush();
    }
  }

  /**
   * Get all teams without a manager in a specific gameworld
   * @param includePlayers Whether to include players in the response
   */
  async getTeamsWithoutManager(includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = {
      manager: null,
    };

    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes'],
            exclude: ['players.team'],
          }
        : undefined
    );
  }

  /**
   * Update a team's selection order
   * @param teamId The team ID
   * @param gameworldId The gameworld ID
   * @param selectionOrder The new selection order array of player IDs
   */
  async updateTeamSelectionOrder(
    teamId: string,
    gameworldId: string,
    selectionOrder: string[]
  ): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const team = await em.findOne(Team, { teamId, gameworldId });
    if (!team) {
      throw new Error(`Team not found: ${teamId}`);
    }

    team.selectionOrder = selectionOrder;
    await em.persistAndFlush(team);
  }

  async updateTeamName(teamId: string, gameworldId: string, teamName: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const team = await em.findOne(Team, { teamId, gameworldId });
    if (!team) {
      throw new Error(`Team not found: ${teamId}`);
    }

    team.teamName = teamName;
    await em.persistAndFlush(team);
  }

  /**
   * Find multiple teams by their IDs for AI processing (minimal data)
   * @param teamIds Array of team IDs to find
   * @returns Array of teams with minimal player data for AI
   */
  async findByIdsForAI(teamIds: string[]): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { teamId: { $in: teamIds } };

    return em.find(Team, where, {
      populate: ['players'], // Only basic player data, no attributes
      exclude: ['players.team'],
    });
  }

  /**
   * Find multiple teams by their IDs
   * @param teamIds Array of team IDs to find
   * @param includePlayers Whether to include players in the response
   * @returns Array of teams matching the provided IDs
   */
  async findByIds(teamIds: string[], includePlayers: boolean = false): Promise<Team[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Team> = { teamId: { $in: teamIds } };

    return em.find(
      Team,
      where,
      includePlayers
        ? {
            populate: ['players', 'players.attributes', 'manager'],
            exclude: ['players.team'],
          }
        : {
            populate: ['manager'],
          }
    );
  }

  async incrementTrainingLevel(team: Team, cost: number): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    // Use nativeUpdate to atomically increment trainingLevel and decrement balance
    await em.nativeUpdate(
      Team,
      { teamId: team.teamId, gameworldId: team.gameworldId },
      {
        trainingLevel: raw('training_level + 1'),
        balance: raw(`balance - ${cost}`),
      }
    );
  }

  getTransactions(days: number, teamId: string): Promise<Transactions[]> {
    const em = this.mikroOrmService.getEntityManager();
    const where: FilterQuery<Transactions> = {
      team: teamId,
      date: { $gte: Date.now() - days * 24 * 60 * 60 * 1000 },
    };
    return em.find(Transactions, where, { orderBy: { date: 'DESC' } });
  }

  async flush() {
    const em = this.mikroOrmService.getEntityManager();
    await em.flush();
  }

  async removeAvailableTeamByTeamId(gameworldId: string, teamId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    const deletedCount = await em.nativeDelete(AvailableTeam, {
      gameworldId,
      teamId,
    });

    if (deletedCount > 0) {
      logger.debug('Removed team from AvailableTeam table', {
        gameworldId,
        teamId,
        deletedCount,
      });
    }
  }

  async addAvailableTeam(gameworldId: string, teamId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();

    // Check if the team is already in the AvailableTeam table
    const existing = await this.getAvailableTeamByTeamId(gameworldId, teamId);
    if (existing) {
      logger.debug('Team already exists in AvailableTeam table', {
        gameworldId,
        teamId,
      });
      return;
    }

    // Create new AvailableTeam record
    const availableTeam = new AvailableTeam();
    availableTeam.id = uuidv4();
    availableTeam.gameworldId = gameworldId;
    availableTeam.teamId = teamId;

    em.persist(availableTeam);
    await em.flush();

    logger.debug('Added team to AvailableTeam table', {
      gameworldId,
      teamId,
      availableTeamId: availableTeam.id,
    });
  }

  async getAvailableTeamByTeamId(
    gameworldId: string,
    teamId: string
  ): Promise<AvailableTeam | null> {
    const em = this.mikroOrmService.getEntityManager();
    return em.findOne(AvailableTeam, {
      gameworldId,
      teamId,
    });
  }

  async reinitialiseTeam(
    teamId: string,
    gameworldId: string,
    playerRepository: PlayerRepository,
    transferRepository: TransferRepository,
    txEm?: SqlEntityManager
  ): Promise<void> {
    const em = txEm ? txEm : this.mikroOrmService.getEntityManager();
    const team = await em.findOneOrFail(
      Team,
      { teamId, gameworldId },
      {
        populate: ['players'],
      }
    );
    if (team.players.length < 15) {
      const sqsClient = new SQS({ tracer });
      await sqsClient.send(
        process.env.PLAYER_QUEUE_URL!,
        JSON.stringify({
          gameworldId: team.gameworldId,
          leagueId: team.league.id,
          tier: team.tier,
          teamId: team.teamId,
          requiredPlayers: 15 - team.players.length,
        })
      );
    } else if (team.players.length > 15) {
      const playersToRemove = team.players.slice(15);
      for (const player of playersToRemove) {
        const playerId = player.playerId;
        // Remove the player from the team
        await playerRepository.removePlayerFromTeam(gameworldId, playerId);
        logger.debug('Removed player from team', { playerId, teamId });

        // Add the player to the transfer list
        await transferRepository.addTransferListedPlayer(player);
      }
    }
    // reset the energy of all players
    // reset the injury status of all players
    // reset the suspension status of all players
    for (const player of team.players.getItems()) {
      player.energy = 100;
      player.injuredUntil = undefined;
      player.suspendedForGames = 0;
      em.persist(player);
    }

    team.balance = STARTING_BALANCE;
    await em.persistAndFlush(team);
  }

  async getAndDeleteAvailableTeam(txEm?: SqlEntityManager): Promise<AvailableTeam | null> {
    const em = txEm ? txEm : this.mikroOrmService.getEntityManager();

    // Get a random available team
    const qb = em.qb(AvailableTeam, 'at');
    const [availableTeam] = await qb
      .orderBy({ [raw(`RANDOM()`)]: 'ASC' })
      .limit(1)
      .getResult();

    if (!availableTeam) {
      return null;
    }

    // Delete the team from available teams
    await em.nativeDelete(AvailableTeam, { id: availableTeam.id });

    return availableTeam;
  }

  async releaseTeamToAvailable(teamId: string, gameworldId: string): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();

    // Check if the team is already in the AvailableTeam table
    const existing = await this.getAvailableTeamByTeamId(gameworldId, teamId);
    if (existing) {
      logger.debug('Team already exists in AvailableTeam table', {
        gameworldId,
        teamId,
      });
      return;
    }

    // Add the team to available teams
    await this.addAvailableTeam(gameworldId, teamId);

    logger.info('Team released back to available teams', {
      gameworldId,
      teamId,
    });
  }
}
