import { Fixture } from '@/entities/Fixture.js';
import { LeagueFactory } from '@/testing/factories/leagueFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { Factory } from 'interface-forge';

export const FixtureFactory = new Factory<Fixture>(
  (factory: Factory<Fixture>, iteration: number) => {
    const played = factory.datatype.boolean();

    return {
      fixtureId: factory.string.uuid(),
      gameworldId: factory.string.uuid(),
      date: factory.date.future().getTime(),
      played: factory.datatype.boolean(),
      score: played
        ? [factory.number.int({ min: 0, max: 5 }), factory.number.int({ min: 0, max: 5 })]
        : undefined,
      scorers: undefined,
      simulatedAt: played ? factory.date.recent().getTime() : undefined,
      seed: played ? factory.number.int({ min: 0, max: 1000000 }) : undefined,
      homeTeam: TeamsFactory.build(),
      awayTeam: TeamsFactory.build(),
      league: LeagueFactory.build(),
    };
  }
);
