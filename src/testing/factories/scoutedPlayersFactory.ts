import { ScoutedPlayer } from '@/entities/ScoutedPlayer.js';
import { PlayerFactory } from '@/testing/factories/playerFactory.js';
import { TeamsFactory } from '@/testing/factories/teamFactory.js';
import { Factory } from 'interface-forge';

export const ScoutedPlayersFactory = new Factory<ScoutedPlayer>(
  (factory: Factory<ScoutedPlayer>, iteration: number) => {
    return {
      gameworldId: factory.string.uuid(),
      team: TeamsFactory.build(),
      player: PlayerFactory.build(),
      scoutedAt: factory.date.past().getTime(),
    };
  }
);
