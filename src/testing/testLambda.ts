/* eslint-disable @typescript-eslint/no-unsafe-argument,@typescript-eslint/no-explicit-any */
import { handler } from '@/functions/auth/getMigrationConflicts.js';
import { initializeDatabase } from '@/storage-interface/database-initializer.js';
import createHttpEvent from '@/testing/createHttpEvent.js';
import { logger } from '@/utils/logger.js';

process.env.STAGE = 'dev';
process.env.DATABASE_TYPE = 'postgres';
process.env.DEBUG_USER_ID = 'a14be5a0-c011-70a1-9bdb-d21eaed0a96b';
process.env.NOTION_ISSUE_TRACKER_DB_ID = '20368369-90e6-80ec-bab2-fdf42be7daf0';
process.env.NOTION_API_KEY = '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';

async function startTest() {
  await initializeDatabase();

  const mockEvent = createHttpEvent({
    body: {},
    queryStringParameters: {
      guestUserId: 'us-east-2:d164f3e0-a467-cdf2-9f5b-c5add8e4ffe5',
    },
    httpMethod: 'GET',
  });
  /* const mockEvent = createSqsEvent([
    {
      messageId: '1bc4389b-7967-437d-9888-4c4ca90d0a1a',
      receiptHandle:
        'AQEBhiL11ZCA8DMut0GAYAk2w5MbEP79CaBasroi8Eth9DAVrnK4HOTmW0O+590ViGvl2A6lz7q2UT7wsj6B03QoyRtvD4WUo6RxB/RKsh82luXWcFbJ94nTuCTNWxYguBt+7ydmeGOavrpEVq+K6mf6ndvaXOrnDKidVpWoaQN6JzCxCXbxg90RbcABVviQttLi1JxeTFLDoINqjrYwfdAuJg1yVu1OnVXnlBMJ6wHEIThFZx+vaiPhyOEC30tH1RfT6MSPwmOQuyJdRmDnphS2N21ci4BHtBvyiSrn+9neEWu/DCIVQKP0nWbdQuxbPUtmKfVAiwBsfhA8K0WuOJ1pXvVgDEmkLWcA4yoPyLHTD5vJSuD5vJ3LAOrVoU29XzdLsHanQY0NiiprMaew0tb5b0FXdHFEJIRE6QPm9DabHLY=',
      body: {
        gameworldId: '84762a81-4b41-4c30-8ef0-c037c41a8540',
        leagueId: '174d44cd-8d4f-4c3a-beba-960e5b30c7af',
        fixtureId: 'db855ba5-b065-46fa-8f80-fc098c7a5e44',
        homeTeamId: '19808f64-b200-43fd-a3f1-da7473653629',
        awayTeamId: 'ae348515-46d8-4647-9685-315bd29cc3cf',
        fixtureDate: 1753902000000,
      },
      attributes: {
        ApproximateReceiveCount: '1',
        AWSTraceHeader:
          'Root=1-688a6bc5-4d2ab3ad580f14c86d416456;Parent=558acbf4316e679a;Sampled=1;Lineage=1:ec60f386:0',
        SentTimestamp: '1753902025871',
        SenderId: 'AROA5CZHT4CSHZDY63U5W:stage-scheduleFixtureSimulation-f13c48f',
        ApproximateFirstReceiveTimestamp: '1753902050680',
      },
      messageAttributes: {},
      md5OfMessageAttributes: null,
      md5OfBody: 'dbad6d47d7c551084cf793bba59332fd',
      eventSource: 'aws:sqs',
      eventSourceARN: 'arn:aws:sqs:us-east-2:899341869220:stage-fixtureQueue-2cda1ba',
      awsRegion: 'us-east-2',
    },
  ]) as any;*/

  const response = await handler(mockEvent, {} as any);
  logger.debug('response', { response });
}

startTest();
