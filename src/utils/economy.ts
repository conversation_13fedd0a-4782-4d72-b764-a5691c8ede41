import { Team } from '@/entities/Team.js';
import { TransactionType } from '@/storage-interface/teams/team-repository.interface.ts';
import { seededRandomIntInRange } from '@/utils/seeded-random.js';

interface MatchIncomeConstants {
  ticketPrice: number;
  avgAttendance: number; // TODO: This should come from the team at some point
  programmePrice: number;
  foodCost: number;
  drinksCost: number;
  merchandiseCost: number;
}

interface MatchExpenseConstants {
  avgWage: number;
  maintenanceRatio: number;
}

const matchIncomeConstants: MatchIncomeConstants[] = [
  // tier 1
  {
    ticketPrice: 15,
    avgAttendance: 20000,
    programmePrice: 2,
    foodCost: 2,
    drinksCost: 2,
    merchandiseCost: 20,
  },
  // tier 2
  {
    ticketPrice: 9,
    avgAttendance: 10000,
    programmePrice: 1,
    foodCost: 1,
    drinksCost: 1.5,
    merchandiseCost: 15,
  },
  // tier 3
  {
    ticketPrice: 5,
    avgAttendance: 5000,
    programmePrice: 0.5,
    foodCost: 0.5,
    drinksCost: 1,
    merchandiseCost: 10,
  },
  // tier 4
  {
    ticketPrice: 3,
    avgAttendance: 3000,
    programmePrice: 0.5,
    foodCost: 0.3,
    drinksCost: 0.75,
    merchandiseCost: 5,
  },
];

const matchExpenditureConstants: MatchExpenseConstants[] = [
  // tier 1
  {
    avgWage: 1500,
    maintenanceRatio: 0.4,
  },
  // tier 2
  {
    avgWage: 750,
    maintenanceRatio: 0.35,
  },
  // tier 3
  {
    avgWage: 300,
    maintenanceRatio: 0.3,
  },
  // tier 4
  {
    avgWage: 100,
    maintenanceRatio: 0.2,
  },
];
const percentageBuyingProgramme = 0.15;
const percentageBuyingFood = 0.6;
const percentageBuyingDrinks = 0.6;
const percentageBuyingMerchandise = 0.2;

// Should only be called for the home team
export function calculateMatchIncome(team: Team): { amount: number; type: TransactionType }[] {
  const tier = team.tier;
  if (tier < 1 || tier > matchIncomeConstants.length) {
    throw new Error(`Invalid tier: ${tier}`);
  }
  const constants = matchIncomeConstants[tier - 1]!;
  const attendance = seededRandomIntInRange(
    constants.avgAttendance * 0.8,
    constants.avgAttendance * 1.2
  );

  const ticketIncome = constants.ticketPrice * attendance;
  const programmeIncome = constants.programmePrice * attendance * percentageBuyingProgramme;
  const foodIncome = constants.foodCost * attendance * percentageBuyingFood;
  const drinksIncome = constants.drinksCost * attendance * percentageBuyingDrinks;
  const merchandiseIncome = constants.merchandiseCost * attendance * percentageBuyingMerchandise;

  return [
    {
      amount: ticketIncome,
      type: TransactionType.TICKET_INCOME,
    },
    {
      amount: programmeIncome,
      type: TransactionType.PROGRAMME_INCOME,
    },
    {
      amount: foodIncome,
      type: TransactionType.FOOD_INCOME,
    },
    {
      amount: drinksIncome,
      type: TransactionType.DRINKS_INCOME,
    },
    {
      amount: merchandiseIncome,
      type: TransactionType.MERCHANDISE_INCOME,
    },
  ];
}

export function calculateMatchExpenses(
  team: Team,
  isHomeTeam: boolean
): { amount: number; type: TransactionType }[] {
  const tier = team.tier;
  if (tier < 1 || tier > matchIncomeConstants.length) {
    throw new Error(`Invalid tier: ${tier}`);
  }
  const constants = matchIncomeConstants[tier - 1]!;
  const expenditureConstants = matchExpenditureConstants[tier - 1]!;
  const groundSize = isHomeTeam ? constants.avgAttendance * 1.2 : 0; // TODO: This should come from the team at some point
  const playerCount = team.players.count();

  return [
    {
      amount: -1 * (expenditureConstants.avgWage * playerCount),
      type: TransactionType.PLAYER_WAGES,
    },
    {
      amount: -1 * (groundSize * expenditureConstants.maintenanceRatio),
      type: TransactionType.GROUND_MAINTENANCE,
    },
  ];
}
