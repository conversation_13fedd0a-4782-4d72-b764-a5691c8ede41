import dotenv from 'dotenv';
import { vi } from 'vitest';
import {
  mockFixtureRepository,
  mockGameworldRepository,
  mockInboxRepository,
  mockLeagueRepository,
  mockManagerRepository,
  mockPlayerRepository,
  mockPurchaseRepository,
  mockScoutingRepository,
  mockScoutingRequestRepository,
  mockTeamRepository,
  mockTrainingRepository,
  mockTransferRepository,
} from './src/testing/mockRepositories.js';

dotenv.config();

process.env.STAGE = 'dev';
process.env.DEBUG_USER_ID = 'debug-user-id';

vi.mock('./src/storage-interface/database-initializer.js', () => ({
  ...vi.importActual('./src/storage-interface/database-initializer.js'),
  initializeDatabase: vi.fn().mockResolvedValue(undefined),
  getScoutingRequestRepository: vi.fn().mockResolvedValue(mockScoutingRequestRepository),
  getScoutingRepository: vi.fn().mockResolvedValue(mockScoutingRepository),
  getLeagueRepository: vi.fn().mockResolvedValue(mockLeagueRepository),
  getTeamRepository: vi.fn().mockResolvedValue(mockTeamRepository),
  getFixtureRepository: vi.fn().mockResolvedValue(mockFixtureRepository),
  getPlayerRepository: vi.fn().mockResolvedValue(mockPlayerRepository),
  getMikroOrmService: vi.fn().mockResolvedValue({
    initialize: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
  }),
  getTransferRepository: vi.fn().mockResolvedValue(mockTransferRepository),
  getGameworldRepository: vi.fn().mockResolvedValue(mockGameworldRepository),
  getManagerRepository: vi.fn().mockResolvedValue(mockManagerRepository),
  getInboxRepository: vi.fn().mockResolvedValue(mockInboxRepository),
  getTrainingRepository: vi.fn().mockResolvedValue(mockTrainingRepository),
  getPurchaseRepository: vi.fn().mockResolvedValue(mockPurchaseRepository),
}));
